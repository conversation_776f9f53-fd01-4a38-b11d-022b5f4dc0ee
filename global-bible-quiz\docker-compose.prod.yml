version: '3.8'

services:
  # Production service optimized for deployment
  bible-quiz-prod:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:3000"   # Map to port 80 for production
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3000
    networks:
      - bible-quiz-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Nginx reverse proxy for production (optional but recommended)
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"   # HTTPS
      - "80:80"     # HTTP redirect
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates
    depends_on:
      - bible-quiz-prod
    networks:
      - bible-quiz-network
    restart: unless-stopped
    profiles:
      - with-nginx

  # Database for production (if needed)
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     - POSTGRES_DB=bible_quiz_prod
  #     - POSTGRES_USER=bible_quiz_user
  #     - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - bible-quiz-network
  #   restart: unless-stopped
  #   secrets:
  #     - db_password

  # Redis for production caching
  # redis:
  #   image: redis:7-alpine
  #   command: redis-server --requirepass ${REDIS_PASSWORD}
  #   environment:
  #     - REDIS_PASSWORD_FILE=/run/secrets/redis_password
  #   networks:
  #     - bible-quiz-network
  #   restart: unless-stopped
  #   secrets:
  #     - redis_password

networks:
  bible-quiz-network:
    driver: bridge

volumes:
  postgres_data:

# secrets:
#   db_password:
#     external: true
#   redis_password:
#     external: true