#!/bin/bash

# Test runner script for Docker environment
# This script runs tests inside Docker containers

echo "🧪 Running Global Bible Quiz Tests in Docker..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Build test container
echo "🔨 Building test container..."
docker-compose build bible-quiz-test

# Run different types of tests
echo "📋 Running test suite:"

# TypeScript compilation test
echo "  ⚙️  Testing TypeScript compilation..."
docker-compose run --rm bible-quiz-test npm run build

# Lint test
echo "  🔍 Running linting tests..."
docker-compose run --rm bible-quiz-test npm run lint

# Unit tests (when added)
# echo "  🧪 Running unit tests..."
# docker-compose run --rm bible-quiz-test npm run test

# Integration tests (when added)  
# echo "  🔗 Running integration tests..."
# docker-compose run --rm bible-quiz-test npm run test:integration

# E2E tests with <PERSON>wright (when added)
# echo "  🎭 Running E2E tests..."
# docker-compose run --rm bible-quiz-test npm run test:e2e

echo ""
echo "✅ All tests completed!"
echo "🧹 Cleaning up test containers..."
docker-compose down --volumes --remove-orphans

echo "🎉 Test run finished!"