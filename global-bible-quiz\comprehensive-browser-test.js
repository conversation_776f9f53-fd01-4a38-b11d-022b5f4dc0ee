const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

async function comprehensiveBrowserTest() {
  let browser;
  let page;
  
  const testResults = {
    timestamp: new Date().toISOString(),
    accessiblePort: null,
    consoleErrors: [],
    hydrationErrors: [],
    networkErrors: [],
    screenshots: [],
    functionalityTests: {},
    testSummary: {}
  };

  try {
    // Install puppeteer if not available
    console.log('🔧 Checking Puppeteer availability...');
    try {
      require.resolve('puppeteer');
    } catch (e) {
      console.log('📦 Installing Puppeteer...');
      await runCommand('npm install puppeteer');
    }

    // Launch browser
    console.log('🚀 Launching browser for comprehensive testing...');
    browser = await puppeteer.launch({
      headless: false, // Keep visible for debugging
      devtools: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--allow-running-insecure-content'
      ]
    });

    page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });

    // Create screenshots directory
    const screenshotsDir = path.join(__dirname, 'browser-test-screenshots');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir);
    }

    // Monitor console logs
    page.on('console', msg => {
      const text = msg.text();
      const type = msg.type();
      
      console.log(`BROWSER CONSOLE [${type}]: ${text}`);
      
      if (type === 'error') {
        testResults.consoleErrors.push({
          type: type,
          text: text,
          timestamp: new Date().toISOString()
        });
      }
      
      // Check for hydration-specific errors
      if (text.includes('hydration') || text.includes('Hydration') || 
          text.includes('client-side') || text.includes('server-side') ||
          text.includes('mismatch') || text.includes('expected server HTML')) {
        testResults.hydrationErrors.push({
          type: type,
          text: text,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Monitor network errors
    page.on('response', response => {
      if (!response.ok()) {
        testResults.networkErrors.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText()
        });
      }
    });

    // Test port accessibility
    console.log('\n📡 Testing port accessibility...');
    const ports = [3000, 3001, 8080, 8000, 5000];
    let accessibleUrl = null;

    for (const port of ports) {
      const url = `http://localhost:${port}`;
      console.log(`Testing port ${port}...`);
      
      try {
        const response = await page.goto(url, { 
          waitUntil: 'networkidle2', 
          timeout: 10000 
        });
        
        if (response && response.ok()) {
          console.log(`✅ Successfully accessed ${url}`);
          accessibleUrl = url;
          testResults.accessiblePort = port;
          break;
        }
      } catch (error) {
        console.log(`❌ Port ${port} not accessible: ${error.message}`);
      }
    }

    if (!accessibleUrl) {
      throw new Error('No accessible ports found. Application may not be running.');
    }

    console.log(`\n🎯 Using ${accessibleUrl} for comprehensive testing`);

    // Wait for page to load
    await page.waitForTimeout(3000);

    // Test 1: Main page functionality
    console.log('\n📄 Testing main page...');
    await page.screenshot({ 
      path: path.join(screenshotsDir, '01-main-page.png'), 
      fullPage: true 
    });
    testResults.screenshots.push('01-main-page.png');

    const pageTitle = await page.title();
    testResults.functionalityTests.pageTitle = pageTitle;
    console.log(`📋 Page title: ${pageTitle}`);

    // Test 2: Navigation to Genesis 1 Quiz
    console.log('\n🎮 Testing Genesis 1 Quiz navigation...');
    
    const genesisLink = await page.$('a[href="/genesis-1-quiz"]');
    if (genesisLink) {
      await genesisLink.click();
      await page.waitForNavigation({ waitUntil: 'networkidle2' });
      await page.waitForTimeout(2000);
      
      await page.screenshot({ 
        path: path.join(screenshotsDir, '02-genesis-quiz-page.png'), 
        fullPage: true 
      });
      testResults.screenshots.push('02-genesis-quiz-page.png');
      
      // Test quiz elements
      const questions = await page.$$eval('h3', elements => 
        elements.filter(el => el.textContent.match(/^\d+\./)).length
      );
      
      const radioButtons = await page.$$('input[type="radio"]');
      const progressBar = await page.$('.bg-blue-600.h-2');
      
      testResults.functionalityTests.genesisQuiz = {
        questionsFound: questions,
        radioButtonsFound: radioButtons.length,
        hasProgressBar: !!progressBar,
        url: page.url()
      };
      
      console.log(`📝 Found ${questions} questions`);
      console.log(`🔘 Found ${radioButtons.length} radio buttons`);
      console.log(`📊 Progress bar present: ${!!progressBar}`);
      
      // Test 3: Answer some questions
      console.log('\n✅ Testing question interaction...');
      
      if (radioButtons.length > 0) {
        // Answer first question
        await radioButtons[0].click();
        await page.waitForTimeout(500);
        
        // Answer second question  
        if (radioButtons.length > 4) {
          await radioButtons[4].click(); // Second question, first option
          await page.waitForTimeout(500);
        }
        
        await page.screenshot({ 
          path: path.join(screenshotsDir, '03-questions-answered.png'), 
          fullPage: true 
        });
        testResults.screenshots.push('03-questions-answered.png');
        
        testResults.functionalityTests.questionInteraction = {
          answeredQuestions: 2,
          interactionWorking: true
        };
      }
      
      // Test 4: Test bypass parameter
      console.log('\n🚀 Testing bypass parameter...');
      const bypassUrl = page.url() + '?start=true';
      await page.goto(bypassUrl, { waitUntil: 'networkidle2' });
      await page.waitForTimeout(2000);
      
      await page.screenshot({ 
        path: path.join(screenshotsDir, '04-bypass-parameter.png'), 
        fullPage: true 
      });
      testResults.screenshots.push('04-bypass-parameter.png');
      
      const bypassContent = await page.content();
      testResults.functionalityTests.bypassParameter = {
        url: bypassUrl,
        contentLength: bypassContent.length,
        working: true
      };
      
      // Test 5: Check for timer functionality
      console.log('\n⏰ Checking for timer elements...');
      const timerElements = await page.$$eval('*', elements => 
        elements.filter(el => 
          el.textContent.includes('timer') || 
          el.textContent.includes('Timer') || 
          el.textContent.includes('time') ||
          el.className?.includes('timer')
        ).length
      );
      
      testResults.functionalityTests.timerElements = timerElements;
      console.log(`⏰ Found ${timerElements} timer-related elements`);
      
    } else {
      console.log('❌ Genesis quiz link not found');
      testResults.functionalityTests.genesisQuizLinkError = 'Link not found';
    }

    // Test 6: Test other quiz pages
    console.log('\n📚 Testing other quiz pages...');
    await page.goto(accessibleUrl, { waitUntil: 'networkidle2' });
    
    const otherQuizLinks = ['/matthew-quiz', '/bible-quizzes', '/genesis-chapters'];
    
    for (let i = 0; i < otherQuizLinks.length; i++) {
      const linkPath = otherQuizLinks[i];
      console.log(`Testing ${linkPath}...`);
      
      try {
        await page.goto(accessibleUrl + linkPath, { waitUntil: 'networkidle2' });
        await page.waitForTimeout(1500);
        
        await page.screenshot({ 
          path: path.join(screenshotsDir, `05-other-quiz-${i + 1}.png`), 
          fullPage: true 
        });
        testResults.screenshots.push(`05-other-quiz-${i + 1}.png`);
        
        const pageContent = await page.content();
        testResults.functionalityTests[`otherQuiz${i + 1}`] = {
          path: linkPath,
          accessible: true,
          contentLength: pageContent.length,
          hasQuizContent: pageContent.includes('quiz') || pageContent.includes('Quiz')
        };
        
      } catch (error) {
        console.log(`❌ Error testing ${linkPath}: ${error.message}`);
        testResults.functionalityTests[`otherQuiz${i + 1}Error`] = error.message;
      }
    }

    // Test 7: Final comprehensive screenshot
    await page.goto(accessibleUrl, { waitUntil: 'networkidle2' });
    await page.screenshot({ 
      path: path.join(screenshotsDir, '99-final-state.png'), 
      fullPage: true 
    });
    testResults.screenshots.push('99-final-state.png');

    // Generate test summary
    testResults.testSummary = {
      totalConsoleErrors: testResults.consoleErrors.length,
      totalHydrationErrors: testResults.hydrationErrors.length,
      totalNetworkErrors: testResults.networkErrors.length,
      totalScreenshots: testResults.screenshots.length,
      mainPageWorking: !!testResults.functionalityTests.pageTitle,
      genesisQuizWorking: !!testResults.functionalityTests.genesisQuiz,
      interactionWorking: !!testResults.functionalityTests.questionInteraction,
      bypassParameterWorking: !!testResults.functionalityTests.bypassParameter
    };

    console.log('\n📊 COMPREHENSIVE TEST SUMMARY:');
    console.log(`✅ Accessible Port: ${testResults.accessiblePort}`);
    console.log(`📋 Page Title: ${testResults.functionalityTests.pageTitle}`);
    console.log(`⚠️  Console Errors: ${testResults.consoleErrors.length}`);
    console.log(`🔄 Hydration Errors: ${testResults.hydrationErrors.length}`);
    console.log(`🌐 Network Errors: ${testResults.networkErrors.length}`);
    console.log(`📸 Screenshots Taken: ${testResults.screenshots.length}`);

    if (testResults.hydrationErrors.length > 0) {
      console.log('\n🔄 HYDRATION ERRORS DETECTED:');
      testResults.hydrationErrors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.type}] ${error.text}`);
      });
    }

    if (testResults.consoleErrors.length > 0) {
      console.log('\n⚠️  CONSOLE ERRORS DETECTED:');
      testResults.consoleErrors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.type}] ${error.text}`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    testResults.testError = error.message;
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  // Save results
  fs.writeFileSync(
    path.join(__dirname, 'comprehensive-test-results.json'),
    JSON.stringify(testResults, null, 2)
  );

  return testResults;
}

function runCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve(stdout || stderr);
      }
    });
  });
}

// Run the comprehensive test
if (require.main === module) {
  comprehensiveBrowserTest().then(results => {
    console.log('\n✅ Comprehensive browser test completed!');
    console.log('📄 Results saved to comprehensive-test-results.json');
    console.log('📸 Screenshots saved in browser-test-screenshots/ directory');
  }).catch(error => {
    console.error('❌ Test execution failed:', error);
  });
}

module.exports = { comprehensiveBrowserTest };