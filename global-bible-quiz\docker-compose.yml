version: '3.8'

services:
  # Development service with hot reload
  bible-quiz-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
    networks:
      - bible-quiz-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Production service
  bible-quiz-prod:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - bible-quiz-network
    restart: unless-stopped
    profiles:
      - production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Testing service for running tests
  bible-quiz-test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=test
      - CI=true
    networks:
      - bible-quiz-network
    profiles:
      - testing
    command: ["npm", "run", "test"]

networks:
  bible-quiz-network:
    driver: bridge

volumes:
  node_modules:
  next_cache: