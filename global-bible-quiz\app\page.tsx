import Image from "next/image";
import Link from "next/link";
import { BIBLE_BOOKS, PLATFORM_STATS } from '@/lib/data/bible-books';
import { BIBLICAL_CHARACTERS, PLATFORM_CHARACTER_STATS } from '@/lib/data/characters';
import { THEME_CATEGORIES, PLATFORM_THEME_STATS } from '@/lib/data/themes';

export default function Home() {
  const featuredBooks = BIBLE_BOOKS.slice(0, 6); // Genesis, Exodus, <PERSON>, <PERSON>, <PERSON>, <PERSON>
  const featuredCharacters = BIBLICAL_CHARACTERS.filter(char => 
    ['abraham', 'moses', 'david', 'jesus', 'paul', 'mary-mother'].includes(char.id)
  );
  const featuredThemes = THEME_CATEGORIES.filter(theme => 
    ['salvation', 'love', 'miracles-of-jesus', 'parables'].includes(theme.id)
  );

  return (
    <div className="min-h-screen bg-gradient-to-b from-amber-50 to-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Test Your
                <span className="text-blue-600 block">Bible Knowledge</span>
              </h1>
              <p className="mt-6 text-xl text-gray-600 leading-relaxed">
                Explore Scripture through comprehensive quizzes covering all 66 books, 
                200+ biblical characters, and 100+ themes. Deepen your faith with 
                interactive learning.
              </p>
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link 
                  href="/bible-quizzes"
                  className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
                >
                  Start Quiz Journey
                </Link>
                <Link 
                  href="/genesis-quiz"
                  className="inline-flex items-center px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors"
                >
                  Try Genesis Quiz
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png"
                  alt="Gentle hands holding an open Bible with divine light"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Statistics */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold">{PLATFORM_STATS.TOTAL_BOOKS}</div>
              <div className="text-blue-100 mt-2">Bible Books</div>
            </div>
            <div>
              <div className="text-4xl font-bold">{PLATFORM_STATS.TOTAL_CHAPTERS}+</div>
              <div className="text-blue-100 mt-2">Chapter Quizzes</div>
            </div>
            <div>
              <div className="text-4xl font-bold">{PLATFORM_CHARACTER_STATS.TOTAL_CHARACTERS}+</div>
              <div className="text-blue-100 mt-2">Characters</div>
            </div>
            <div>
              <div className="text-4xl font-bold">{PLATFORM_THEME_STATS.TOTAL_THEMES}+</div>
              <div className="text-blue-100 mt-2">Themes</div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Bible Books */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Popular Bible Book Quizzes
            </h2>
            <p className="text-xl text-gray-600">
              Start with these beloved books of Scripture
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredBooks.map((book) => (
              <Link key={book.id} href={`/${book.slug}-quiz`}>
                <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">{book.name}</h3>
                    <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                      {book.chapters} chapters
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {book.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 capitalize">
                      {book.difficulty} • {book.estimatedTime}min
                    </span>
                    <div className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                      Take Quiz
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Prayer and Study Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png"
                  alt="Man in deep prayer with divine light"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Strengthen Your Faith Through Scripture
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Our comprehensive quiz platform is designed to help you dive deeper 
                into God's Word. Whether you're a new believer or a seasoned student 
                of Scripture, you'll find quizzes that challenge and inspire.
              </p>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-gray-700">Progressive difficulty levels from beginner to advanced</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-gray-700">Detailed explanations with Bible verse references</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-gray-700">Track your progress across all quiz categories</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Characters */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Meet Biblical Characters
            </h2>
            <p className="text-xl text-gray-600">
              Test your knowledge of the heroes and heroines of faith
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredCharacters.map((character) => (
              <Link key={character.id} href={`/${character.slug}-quiz`}>
                <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{character.name}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {character.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="bg-amber-100 text-amber-800 text-sm px-3 py-1 rounded-full capitalize">
                      {character.testament} Testament
                    </span>
                    <div className="bg-amber-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                      Learn More
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Themes */}
      <section className="py-20 bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Explore Biblical Themes
            </h2>
            <p className="text-xl text-gray-600">
              Discover the great themes that run throughout Scripture
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredThemes.map((theme) => (
              <Link key={theme.id} href={`/${theme.slug}-quiz`}>
                <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{theme.name}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {theme.description}
                  </p>
                  <div className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium inline-block">
                    Explore Theme
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Begin Your Bible Quiz Journey?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of believers who are growing in their knowledge of God's Word 
            through our comprehensive quiz platform.
          </p>
          <Link 
            href="/bible-quizzes"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg"
          >
            Browse All Quizzes
          </Link>
        </div>
      </section>
    </div>
  );
}