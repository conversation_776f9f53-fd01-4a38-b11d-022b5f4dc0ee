# Global Bible Quiz - Docker Development & Deployment Makefile

.PHONY: help dev prod test clean deploy-aws deploy-do logs stop build

# Default target
help: ## Show this help message
	@echo "Global Bible Quiz - Docker Commands"
	@echo "=================================="
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development commands
dev: ## Start development environment with hot reload
	@echo "🚀 Starting development environment..."
	@chmod +x docker-scripts/dev-start.sh
	@./docker-scripts/dev-start.sh

dev-build: ## Build development containers
	@echo "🔨 Building development containers..."
	@docker-compose -f docker-compose.dev.yml build

dev-logs: ## Show development logs
	@docker-compose -f docker-compose.dev.yml logs -f

dev-stop: ## Stop development environment
	@echo "🛑 Stopping development environment..."
	@docker-compose -f docker-compose.dev.yml down

# Production commands
prod: ## Start production environment
	@echo "🚀 Starting production environment..."
	@chmod +x docker-scripts/prod-start.sh
	@./docker-scripts/prod-start.sh

prod-build: ## Build production containers
	@echo "🔨 Building production containers..."
	@docker-compose -f docker-compose.prod.yml build

prod-logs: ## Show production logs
	@docker-compose -f docker-compose.prod.yml logs -f

prod-stop: ## Stop production environment
	@echo "🛑 Stopping production environment..."
	@docker-compose -f docker-compose.prod.yml down

# Testing commands
test: ## Run all tests in Docker
	@echo "🧪 Running tests..."
	@chmod +x docker-scripts/test-run.sh
	@./docker-scripts/test-run.sh

test-build: ## Build test containers
	@docker-compose build bible-quiz-test

# Deployment commands
deploy-aws: ## Deploy to AWS ECS
	@echo "☁️  Deploying to AWS..."
	@chmod +x docker-scripts/deploy-aws.sh
	@./docker-scripts/deploy-aws.sh

deploy-do: ## Deploy to Digital Ocean
	@echo "🌊 Deploying to Digital Ocean..."
	@chmod +x docker-scripts/deploy-digitalocean.sh
	@./docker-scripts/deploy-digitalocean.sh

# Utility commands
build: ## Build all containers
	@echo "🔨 Building all containers..."
	@docker-compose -f docker-compose.dev.yml build
	@docker-compose -f docker-compose.prod.yml build

clean: ## Clean up Docker containers, images, and volumes
	@echo "🧹 Cleaning up Docker resources..."
	@docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans
	@docker-compose -f docker-compose.prod.yml down --volumes --remove-orphans
	@docker system prune -f
	@docker volume prune -f

logs: ## Show logs for running services
	@echo "📝 Current container logs:"
	@docker-compose -f docker-compose.dev.yml logs --tail=50

stop: ## Stop all running containers
	@echo "🛑 Stopping all containers..."
	@docker-compose -f docker-compose.dev.yml down
	@docker-compose -f docker-compose.prod.yml down

restart-dev: dev-stop dev ## Restart development environment

restart-prod: prod-stop prod ## Restart production environment

# Health check
health: ## Check health of running containers
	@echo "🏥 Checking container health..."
	@docker-compose -f docker-compose.dev.yml ps
	@docker-compose -f docker-compose.prod.yml ps

# Quick development setup
setup: ## Initial setup for development
	@echo "⚙️  Setting up development environment..."
	@echo "📦 Installing dependencies..."
	@npm install
	@echo "🔨 Building development containers..."
	@make dev-build
	@echo "✅ Setup complete! Run 'make dev' to start development."

# Show running containers
ps: ## Show running containers
	@echo "📋 Running containers:"
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"