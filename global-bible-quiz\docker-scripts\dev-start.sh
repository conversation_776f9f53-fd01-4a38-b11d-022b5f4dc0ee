#!/bin/bash

# Development Docker startup script
# This script starts the development environment with hot reload

echo "🚀 Starting Global Bible Quiz Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Remove any dangling images
echo "🧹 Cleaning up dangling images..."
docker image prune -f

# Build and start development containers
echo "🔨 Building and starting development containers..."
docker-compose -f docker-compose.dev.yml up --build -d

# Show running containers
echo "📋 Running containers:"
docker-compose -f docker-compose.dev.yml ps

# Show logs
echo "📝 Container logs (press Ctrl+C to exit log view):"
echo "   Development server will be available at: http://localhost:3000"
echo "   Auto-refresh is enabled - changes will reflect immediately"
echo ""

# Follow logs
docker-compose -f docker-compose.dev.yml logs -f bible-quiz-dev