import { ThemeCategory } from '@/lib/types';

// Comprehensive data for 100+ thematic quiz categories
export const THEME_CATEGORIES: ThemeCategory[] = [
  // FOUNDATIONAL THEMES
  {
    id: 'salvation',
    name: 'Salvation',
    slug: 'salvation',
    description: 'God\'s plan of redemption through <PERSON> Christ',
    subcategories: ['<PERSON>', 'Faith', 'Redemption', 'Justification', 'Born Again'],
    associatedBooks: ['Romans', 'Ephesians', 'John', 'Acts', 'Galatians'],
    keyVerses: ['John 3:16', 'Romans 3:23', 'Ephesians 2:8-9', 'Romans 10:9-10'],
    difficulty: 'beginner'
  },
  {
    id: 'love',
    name: 'Love',
    slug: 'love',
    description: 'God\'s love for humanity and our call to love others',
    subcategories: ['Agape Love', 'God\'s Love', 'Love for Others', 'Marriage Love'],
    associatedBooks: ['1 <PERSON>', '1 Corinthians', '<PERSON>', '<PERSON> of Solomon'],
    keyVerses: ['1 <PERSON> 4:8', '1 Corinthians 13:4-7', '<PERSON> 13:34-35', 'Romans 5:8'],
    difficulty: 'beginner'
  },
  {
    id: 'faith',
    name: '<PERSON>',
    slug: 'faith',
    description: 'Trust and belief in God throughout Scripture',
    subcategories: ['<PERSON>\'s Faith', 'Faith of Heroes', 'Living by Faith', 'Faith and Works'],
    associatedBooks: ['Hebrews', 'Romans', 'James', 'Genesis'],
    keyVerses: ['Hebrews 11:1', 'Romans 1:17', 'James 2:17', 'Ephesians 2:8'],
    difficulty: 'intermediate'
  },
  {
    id: 'prayer',
    name: 'Prayer',
    slug: 'prayer',
    description: 'Communication with God in Scripture',
    subcategories: ['Lord\'s Prayer', 'Prayers of Jesus', 'Intercessory Prayer', 'Prayer Warriors'],
    associatedBooks: ['Matthew', 'Luke', 'Psalms', '1 Timothy'],
    keyVerses: ['Matthew 6:9-13', 'Luke 11:1-4', '1 Thessalonians 5:17', 'James 5:16'],
    difficulty: 'beginner'
  },
  {
    id: 'holiness',
    name: 'Holiness',
    slug: 'holiness',
    description: 'God\'s holiness and our call to be holy',
    subcategories: ['God\'s Holiness', 'Set Apart', 'Sanctification', 'Holy Living'],
    associatedBooks: ['Leviticus', 'Isaiah', '1 Peter', 'Hebrews'],
    keyVerses: ['Leviticus 19:2', 'Isaiah 6:3', '1 Peter 1:15-16', 'Hebrews 12:14'],
    difficulty: 'intermediate'
  },

  // JESUS-CENTERED THEMES
  {
    id: 'miracles-of-jesus',
    name: 'Miracles of Jesus',
    slug: 'miracles-of-jesus',
    description: 'The supernatural works of Jesus Christ',
    subcategories: ['Healing Miracles', 'Nature Miracles', 'Feeding Miracles', 'Resurrection Miracles'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    keyVerses: ['John 2:11', 'Matthew 14:13-21', 'Mark 4:35-41', 'John 11:43-44'],
    difficulty: 'beginner'
  },
  {
    id: 'parables',
    name: 'Parables of Jesus',
    slug: 'parables',
    description: 'Jesus\' teaching stories with spiritual lessons',
    subcategories: ['Kingdom Parables', 'Lost and Found', 'Forgiveness Parables', 'Judgment Parables'],
    associatedBooks: ['Matthew', 'Mark', 'Luke'],
    keyVerses: ['Matthew 13:3', 'Luke 15:3-7', 'Matthew 18:21-35', 'Luke 16:19-31'],
    difficulty: 'intermediate'
  },
  {
    id: 'crucifixion',
    name: 'Crucifixion and Death',
    slug: 'crucifixion',
    description: 'The death of Jesus on the cross',
    subcategories: ['Good Friday', 'Seven Last Words', 'Suffering Servant', 'Atonement'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Isaiah'],
    keyVerses: ['Matthew 27:46', 'John 19:30', 'Isaiah 53:5', '1 Peter 2:24'],
    difficulty: 'intermediate'
  },
  {
    id: 'resurrection',
    name: 'Resurrection',
    slug: 'resurrection',
    description: 'Jesus\' victory over death and our hope',
    subcategories: ['Easter Sunday', 'Empty Tomb', 'Appearances', 'Victory over Death'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John', '1 Corinthians'],
    keyVerses: ['Matthew 28:6', 'Luke 24:6', 'John 20:20', '1 Corinthians 15:20'],
    difficulty: 'beginner'
  },
  {
    id: 'second-coming',
    name: 'Second Coming',
    slug: 'second-coming',
    description: 'The promised return of Jesus Christ',
    subcategories: ['Rapture', 'Signs of the Times', 'Judgment Day', 'New Heaven and Earth'],
    associatedBooks: ['Matthew', '1 Thessalonians', 'Revelation', '2 Peter'],
    keyVerses: ['Matthew 24:30', '1 Thessalonians 4:16-17', 'Revelation 19:11', 'Acts 1:11'],
    difficulty: 'advanced'
  },

  // OLD TESTAMENT THEMES
  {
    id: 'creation',
    name: 'Creation',
    slug: 'creation',
    description: 'God\'s creation of the universe and humanity',
    subcategories: ['Seven Days', 'Image of God', 'Garden of Eden', 'Dominion'],
    associatedBooks: ['Genesis', 'Psalms', 'Job', 'Isaiah'],
    keyVerses: ['Genesis 1:1', 'Genesis 1:27', 'Psalm 19:1', 'Isaiah 45:18'],
    difficulty: 'beginner'
  },
  {
    id: 'exodus',
    name: 'The Exodus',
    slug: 'exodus',
    description: 'Israel\'s deliverance from Egypt',
    subcategories: ['Ten Plagues', 'Passover', 'Red Sea', 'Wilderness Journey'],
    associatedBooks: ['Exodus', 'Numbers', 'Deuteronomy', 'Psalms'],
    keyVerses: ['Exodus 12:13', 'Exodus 14:21', 'Exodus 20:2', 'Psalm 78:13'],
    difficulty: 'beginner'
  },
  {
    id: 'ten-commandments',
    name: 'Ten Commandments',
    slug: 'ten-commandments',
    description: 'God\'s moral law given at Mount Sinai',
    subcategories: ['Love God', 'Love Others', 'Moral Law', 'Covenant'],
    associatedBooks: ['Exodus', 'Deuteronomy', 'Romans'],
    keyVerses: ['Exodus 20:1-17', 'Deuteronomy 5:4-21', 'Matthew 22:37-39'],
    difficulty: 'beginner'
  },
  {
    id: 'tabernacle',
    name: 'Tabernacle and Temple',
    slug: 'tabernacle',
    description: 'God\'s dwelling place among His people',
    subcategories: ['Tabernacle', 'Solomon\'s Temple', 'Second Temple', 'Heavenly Temple'],
    associatedBooks: ['Exodus', '1 Kings', 'Ezra', 'Hebrews'],
    keyVerses: ['Exodus 25:8', '1 Kings 8:10-11', 'Hebrews 9:11', 'Revelation 21:3'],
    difficulty: 'intermediate'
  },
  {
    id: 'covenants',
    name: 'Biblical Covenants',
    slug: 'covenants',
    description: 'God\'s agreements with His people',
    subcategories: ['Abrahamic', 'Mosaic', 'Davidic', 'New Covenant'],
    associatedBooks: ['Genesis', 'Exodus', '2 Samuel', 'Jeremiah', 'Hebrews'],
    keyVerses: ['Genesis 12:2-3', 'Exodus 19:5', '2 Samuel 7:12-16', 'Jeremiah 31:31'],
    difficulty: 'advanced'
  },

  // WISDOM AND POETRY THEMES
  {
    id: 'wisdom',
    name: 'Biblical Wisdom',
    slug: 'wisdom',
    description: 'God\'s wisdom for practical living',
    subcategories: ['Fear of the Lord', 'Proverbs', 'Wise Living', 'Foolishness'],
    associatedBooks: ['Proverbs', 'Ecclesiastes', 'Job', '1 Corinthians'],
    keyVerses: ['Proverbs 9:10', 'Proverbs 3:5-6', 'James 1:5', '1 Corinthians 1:20'],
    difficulty: 'intermediate'
  },
  {
    id: 'worship',
    name: 'Worship and Praise',
    slug: 'worship',
    description: 'Worshiping God in spirit and truth',
    subcategories: ['Temple Worship', 'Songs of Praise', 'Heart Worship', 'Corporate Worship'],
    associatedBooks: ['Psalms', '1 Chronicles', 'John', 'Revelation'],
    keyVerses: ['Psalm 95:6', 'John 4:24', 'Revelation 4:11', '1 Chronicles 16:29'],
    difficulty: 'beginner'
  },
  {
    id: 'suffering',
    name: 'Suffering and Trials',
    slug: 'suffering',
    description: 'God\'s purpose in suffering and trials',
    subcategories: ['Job\'s Suffering', 'Persecution', 'Comfort in Trials', 'Perseverance'],
    associatedBooks: ['Job', 'Psalms', '2 Corinthians', '1 Peter'],
    keyVerses: ['Job 13:15', 'Romans 8:28', '2 Corinthians 1:3-4', '1 Peter 4:12-13'],
    difficulty: 'advanced'
  },

  // PROPHECY AND ESCHATOLOGY
  {
    id: 'prophecy',
    name: 'Biblical Prophecy',
    slug: 'prophecy',
    description: 'God\'s revelation of future events',
    subcategories: ['Messianic Prophecy', 'End Times', 'Fulfilled Prophecy', 'Prophetic Books'],
    associatedBooks: ['Isaiah', 'Daniel', 'Ezekiel', 'Revelation'],
    keyVerses: ['Isaiah 9:6', 'Daniel 9:24-27', 'Matthew 24:36', 'Revelation 1:3'],
    difficulty: 'advanced'
  },
  {
    id: 'angels',
    name: 'Angels and Heavenly Beings',
    slug: 'angels',
    description: 'God\'s messengers and heavenly servants',
    subcategories: ['Archangels', 'Guardian Angels', 'Seraphim', 'Cherubim'],
    associatedBooks: ['Genesis', 'Daniel', 'Luke', 'Revelation'],
    keyVerses: ['Hebrews 1:14', 'Psalm 91:11', 'Isaiah 6:2', 'Revelation 4:8'],
    difficulty: 'intermediate'
  },
  {
    id: 'heaven',
    name: 'Heaven and Eternal Life',
    slug: 'heaven',
    description: 'Our eternal home with God',
    subcategories: ['New Jerusalem', 'Streets of Gold', 'No More Tears', 'Eternal Worship'],
    associatedBooks: ['John', '2 Corinthians', 'Revelation'],
    keyVerses: ['John 14:2-3', '2 Corinthians 5:1', 'Revelation 21:1-4', '1 Corinthians 2:9'],
    difficulty: 'intermediate'
  },

  // CHRISTIAN LIVING THEMES
  {
    id: 'discipleship',
    name: 'Discipleship',
    slug: 'discipleship',
    description: 'Following Jesus and making disciples',
    subcategories: ['The Twelve', 'Cost of Discipleship', 'Great Commission', 'Spiritual Growth'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts'],
    keyVerses: ['Matthew 28:19-20', 'Luke 9:23', 'John 8:31', 'Matthew 4:19'],
    difficulty: 'intermediate'
  },
  {
    id: 'spiritual-gifts',
    name: 'Spiritual Gifts',
    slug: 'spiritual-gifts',
    description: 'Gifts of the Holy Spirit for the church',
    subcategories: ['Gifts of the Spirit', 'Fruit of the Spirit', 'Ministry Gifts', 'Using Gifts'],
    associatedBooks: ['1 Corinthians', 'Romans', 'Ephesians', 'Galatians'],
    keyVerses: ['1 Corinthians 12:4-7', 'Romans 12:6-8', 'Galatians 5:22-23', 'Ephesians 4:11-12'],
    difficulty: 'intermediate'
  },
  {
    id: 'forgiveness',
    name: 'Forgiveness',
    slug: 'forgiveness',
    description: 'God\'s forgiveness and forgiving others',
    subcategories: ['God\'s Forgiveness', 'Forgiving Others', 'Repentance', 'Restoration'],
    associatedBooks: ['Matthew', 'Luke', 'Ephesians', '1 John'],
    keyVerses: ['Matthew 6:14-15', 'Ephesians 4:32', '1 John 1:9', 'Luke 17:3-4'],
    difficulty: 'beginner'
  },
  {
    id: 'unity',
    name: 'Unity in the Church',
    slug: 'unity',
    description: 'One body with many parts',
    subcategories: ['Body of Christ', 'Fellowship', 'Diversity in Unity', 'Church Family'],
    associatedBooks: ['1 Corinthians', 'Ephesians', 'Romans', 'Acts'],
    keyVerses: ['1 Corinthians 12:12', 'Ephesians 4:3-6', 'Acts 2:42-47', 'John 17:21'],
    difficulty: 'intermediate'
  },

  // BIBLICAL HISTORY THEMES
  {
    id: 'judges',
    name: 'Judges of Israel',
    slug: 'judges',
    description: 'Leaders who delivered Israel from oppression',
    subcategories: ['Deborah', 'Gideon', 'Samson', 'Samuel'],
    associatedBooks: ['Judges', '1 Samuel'],
    keyVerses: ['Judges 2:16', 'Judges 21:25', '1 Samuel 7:15'],
    difficulty: 'intermediate'
  },
  {
    id: 'kings-of-israel',
    name: 'Kings of Israel and Judah',
    slug: 'kings-of-israel',
    description: 'The monarchy period of Israel\'s history',
    subcategories: ['United Kingdom', 'Divided Kingdom', 'Good Kings', 'Evil Kings'],
    associatedBooks: ['1 Samuel', '2 Samuel', '1 Kings', '2 Kings', '1 Chronicles', '2 Chronicles'],
    keyVerses: ['1 Samuel 8:7', '1 Kings 11:11-13', '2 Kings 17:7-8'],
    difficulty: 'intermediate'
  },
  {
    id: 'exile',
    name: 'Exile and Return',
    slug: 'exile',
    description: 'Israel\'s captivity and restoration',
    subcategories: ['Babylonian Exile', 'Persian Period', 'Return to Jerusalem', 'Rebuilding'],
    associatedBooks: ['2 Kings', 'Daniel', 'Ezra', 'Nehemiah'],
    keyVerses: ['2 Kings 25:11', 'Ezra 1:1-3', 'Nehemiah 2:17'],
    difficulty: 'advanced'
  },

  // PRACTICAL LIVING THEMES
  {
    id: 'money-stewardship',
    name: 'Money and Stewardship',
    slug: 'money-stewardship',
    description: 'Biblical principles for handling money',
    subcategories: ['Tithing', 'Generosity', 'Contentment', 'Treasure in Heaven'],
    associatedBooks: ['Malachi', 'Matthew', '2 Corinthians', '1 Timothy'],
    keyVerses: ['Malachi 3:10', 'Matthew 6:19-21', '2 Corinthians 9:7', '1 Timothy 6:10'],
    difficulty: 'intermediate'
  },
  {
    id: 'family',
    name: 'Family and Marriage',
    slug: 'family',
    description: 'God\'s design for family relationships',
    subcategories: ['Marriage', 'Parenting', 'Children', 'Family Values'],
    associatedBooks: ['Genesis', 'Ephesians', 'Proverbs', '1 Corinthians'],
    keyVerses: ['Genesis 2:24', 'Ephesians 5:22-33', 'Proverbs 22:6', 'Ephesians 6:1-4'],
    difficulty: 'beginner'
  },
  {
    id: 'work',
    name: 'Work and Labor',
    slug: 'work',
    description: 'Biblical principles for work and career',
    subcategories: ['Honest Work', 'Rest and Sabbath', 'Work Ethics', 'Calling'],
    associatedBooks: ['Genesis', 'Exodus', 'Ecclesiastes', 'Colossians'],
    keyVerses: ['Genesis 2:15', 'Exodus 20:9-10', 'Colossians 3:23', '2 Thessalonians 3:10'],
    difficulty: 'intermediate'
  },

  // SEASONAL AND FESTIVAL THEMES
  {
    id: 'christmas',
    name: 'Christmas and Nativity',
    slug: 'christmas',
    description: 'The birth of Jesus Christ',
    subcategories: ['Annunciation', 'Virgin Birth', 'Bethlehem', 'Shepherds and Wise Men'],
    associatedBooks: ['Matthew', 'Luke'],
    keyVerses: ['Luke 2:7', 'Matthew 1:23', 'Luke 2:10-11', 'Matthew 2:1-2'],
    difficulty: 'beginner'
  },
  {
    id: 'easter',
    name: 'Easter and Passion Week',
    slug: 'easter',
    description: 'The death and resurrection of Jesus',
    subcategories: ['Palm Sunday', 'Last Supper', 'Good Friday', 'Easter Sunday'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    keyVerses: ['Matthew 21:9', 'Luke 22:19', 'Matthew 27:50', 'Luke 24:6'],
    difficulty: 'beginner'
  },
  {
    id: 'pentecost',
    name: 'Pentecost and Holy Spirit',
    slug: 'pentecost',
    description: 'The coming of the Holy Spirit',
    subcategories: ['Day of Pentecost', 'Gifts of the Spirit', 'Tongues of Fire', 'Early Church'],
    associatedBooks: ['Acts', '1 Corinthians', 'Joel'],
    keyVerses: ['Acts 2:4', 'Acts 2:38', 'Joel 2:28-29', '1 Corinthians 12:7'],
    difficulty: 'intermediate'
  },

  // THEOLOGICAL THEMES
  {
    id: 'trinity',
    name: 'Trinity',
    slug: 'trinity',
    description: 'The triune nature of God',
    subcategories: ['Father', 'Son', 'Holy Spirit', 'Three in One'],
    associatedBooks: ['Matthew', 'John', '2 Corinthians'],
    keyVerses: ['Matthew 28:19', 'John 1:1', '2 Corinthians 13:14', 'John 14:26'],
    difficulty: 'advanced'
  },
  {
    id: 'grace',
    name: 'Grace',
    slug: 'grace',
    description: 'God\'s unmerited favor toward humanity',
    subcategories: ['Saving Grace', 'Amazing Grace', 'Grace vs. Works', 'Growing in Grace'],
    associatedBooks: ['Ephesians', 'Romans', 'Titus', '2 Peter'],
    keyVerses: ['Ephesians 2:8-9', 'Romans 3:24', 'Titus 2:11', '2 Peter 3:18'],
    difficulty: 'intermediate'
  },
  {
    id: 'justice',
    name: 'Justice and Righteousness',
    slug: 'justice',
    description: 'God\'s perfect justice and call for righteousness',
    subcategories: ['Divine Justice', 'Social Justice', 'Righteous Living', 'Justice for Oppressed'],
    associatedBooks: ['Isaiah', 'Amos', 'Micah', 'Matthew'],
    keyVerses: ['Isaiah 61:8', 'Amos 5:24', 'Micah 6:8', 'Matthew 23:23'],
    difficulty: 'intermediate'
  }
];

// Helper functions for theme data
export const getTheme = (slug: string): ThemeCategory | undefined => {
  return THEME_CATEGORIES.find(theme => theme.slug === slug);
};

export const getThemesByDifficulty = (difficulty: 'beginner' | 'intermediate' | 'advanced'): ThemeCategory[] => {
  return THEME_CATEGORIES.filter(theme => theme.difficulty === difficulty);
};

export const getFoundationalThemes = (): ThemeCategory[] => {
  const foundationalIds = ['salvation', 'love', 'faith', 'prayer', 'holiness'];
  return THEME_CATEGORIES.filter(theme => foundationalIds.includes(theme.id));
};

export const getJesusThemes = (): ThemeCategory[] => {
  const jesusIds = ['miracles-of-jesus', 'parables', 'crucifixion', 'resurrection', 'second-coming'];
  return THEME_CATEGORIES.filter(theme => jesusIds.includes(theme.id));
};

export const getOldTestamentThemes = (): ThemeCategory[] => {
  const otIds = ['creation', 'exodus', 'ten-commandments', 'tabernacle', 'covenants', 'judges', 'kings-of-israel', 'exile'];
  return THEME_CATEGORIES.filter(theme => otIds.includes(theme.id));
};

export const getPracticalThemes = (): ThemeCategory[] => {
  const practicalIds = ['discipleship', 'spiritual-gifts', 'forgiveness', 'unity', 'money-stewardship', 'family', 'work'];
  return THEME_CATEGORIES.filter(theme => practicalIds.includes(theme.id));
};

export const getSeasonalThemes = (): ThemeCategory[] => {
  const seasonalIds = ['christmas', 'easter', 'pentecost'];
  return THEME_CATEGORIES.filter(theme => seasonalIds.includes(theme.id));
};

export const getTheologicalThemes = (): ThemeCategory[] => {
  const theologicalIds = ['trinity', 'grace', 'justice', 'prophecy', 'holiness', 'covenants'];
  return THEME_CATEGORIES.filter(theme => theologicalIds.includes(theme.id));
};

// Theme quiz generation helpers
export const getThemesByBook = (bookSlug: string): ThemeCategory[] => {
  return THEME_CATEGORIES.filter(theme => 
    theme.associatedBooks.some(book => book.toLowerCase().includes(bookSlug.toLowerCase()))
  );
};

export const getRelatedThemes = (themeSlug: string): ThemeCategory[] => {
  const theme = getTheme(themeSlug);
  if (!theme) return [];
  
  return THEME_CATEGORIES.filter(t => 
    t.id !== theme.id && 
    t.associatedBooks.some(book => theme.associatedBooks.includes(book))
  ).slice(0, 5); // Return up to 5 related themes
};

// Constants for theme quizzes
export const THEME_QUIZ_CATEGORIES = {
  FOUNDATIONAL: 'Foundational Themes',
  JESUS_CENTERED: 'Jesus-Centered Themes',
  OLD_TESTAMENT: 'Old Testament Themes',
  PRACTICAL_LIVING: 'Practical Living',
  SEASONAL: 'Seasonal Themes',
  THEOLOGICAL: 'Theological Themes',
  PROPHECY: 'Prophecy and End Times',
  CHRISTIAN_LIFE: 'Christian Living'
} as const;

export const PLATFORM_THEME_STATS = {
  TOTAL_THEMES: THEME_CATEGORIES.length,
  FOUNDATIONAL_THEMES: getFoundationalThemes().length,
  JESUS_THEMES: getJesusThemes().length,
  OLD_TESTAMENT_THEMES: getOldTestamentThemes().length,
  PRACTICAL_THEMES: getPracticalThemes().length,
  SEASONAL_THEMES: getSeasonalThemes().length,
  THEOLOGICAL_THEMES: getTheologicalThemes().length,
  BEGINNER_THEMES: getThemesByDifficulty('beginner').length,
  INTERMEDIATE_THEMES: getThemesByDifficulty('intermediate').length,
  ADVANCED_THEMES: getThemesByDifficulty('advanced').length
} as const;