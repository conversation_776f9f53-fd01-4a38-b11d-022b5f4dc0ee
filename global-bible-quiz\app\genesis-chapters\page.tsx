import Image from 'next/image';
import Link from 'next/link';
import { getBibleBook, getBookChapterRange } from '@/lib/data/bible-books';

// Chapter descriptions for Genesis
const GENESIS_CHAPTERS = [
  { chapter: 1, title: "Creation of the Universe", description: "God creates the heavens, earth, and all living things in six days", keyEvents: ["Light and darkness", "Sky and seas", "Land and plants", "Sun, moon, stars", "Sea creatures and birds", "Land animals and humans"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 2, title: "Garden of Eden", description: "God forms man, creates woman, and establishes marriage", keyEvents: ["Man formed from dust", "Garden planted", "Tree of life", "Woman created", "First marriage"], difficulty: "easy", estimatedTime: 4 },
  { chapter: 3, title: "The Fall of Man", description: "The serpent tempts Eve, sin enters the world", keyEvents: ["Serpent's temptation", "Eating forbidden fruit", "Eyes opened", "Hiding from God", "Curse and promise"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 4, title: "<PERSON> and Abel", description: "The first murder and its consequences", keyEvents: ["Two brothers", "Different offerings", "God's favor", "Cain's anger", "First murder"], difficulty: "easy", estimatedTime: 4 },
  { chapter: 5, title: "Genealogy from Adam", description: "The genealogy from Adam to Noah", keyEvents: ["Adam's descendants", "Long lifespans", "Enoch walked with God", "Methuselah lived 969 years", "Noah born"], difficulty: "medium", estimatedTime: 3 },
  { chapter: 6, title: "Noah and the Ark", description: "God decides to flood the earth and instructs Noah to build an ark", keyEvents: ["Wickedness increases", "God grieves", "Noah finds favor", "Ark instructions", "Animal pairs"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 7, title: "The Great Flood Begins", description: "Noah enters the ark and the flood waters come", keyEvents: ["Entering the ark", "Rain begins", "40 days and nights", "All flesh dies", "Waters prevail"], difficulty: "easy", estimatedTime: 4 },
  { chapter: 8, title: "The Flood Ends", description: "The waters recede and Noah leaves the ark", keyEvents: ["Waters decrease", "Ark rests on Ararat", "Dove sent out", "Olive leaf", "Leaving the ark"], difficulty: "easy", estimatedTime: 4 },
  { chapter: 9, title: "God's Covenant with Noah", description: "God makes a covenant with Noah and gives the rainbow sign", keyEvents: ["Noah's altar", "New diet allowed", "Rainbow covenant", "Noah's vineyard", "Curse on Canaan"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 10, title: "Table of Nations", description: "The descendants of Noah's three sons spread across the earth", keyEvents: ["Japheth's descendants", "Ham's descendants", "Shem's descendants", "Nations formed", "Languages divided"], difficulty: "hard", estimatedTime: 3 },
  { chapter: 11, title: "Tower of Babel", description: "Humanity tries to build a tower to heaven; God confuses their language", keyEvents: ["One language", "Tower building", "Reaching heaven", "God confuses language", "People scattered"], difficulty: "easy", estimatedTime: 4 },
  { chapter: 12, title: "God Calls Abram", description: "God calls Abram to leave his country and go to a new land", keyEvents: ["God's call", "Leave your country", "Great nation promise", "Journey to Canaan", "Famine in the land"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 13, title: "Abram and Lot Separate", description: "Abram and Lot separate due to conflict between their herdsmen", keyEvents: ["Return from Egypt", "Conflict arises", "Lot chooses Jordan valley", "Abram stays in Canaan", "God's renewed promise"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 14, title: "Abram Rescues Lot", description: "Abram rescues Lot from captivity and meets Melchizedek", keyEvents: ["Kings at war", "Lot captured", "Abram's rescue", "Meeting Melchizedek", "Tithing to priest"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 15, title: "God's Covenant with Abram", description: "God makes a covenant with Abram promising descendants and land", keyEvents: ["Don't be afraid", "Stars in heaven", "Abram believed", "Covenant ceremony", "400 years slavery foretold"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 16, title: "Hagar and Ishmael", description: "Sarai gives Hagar to Abram; Ishmael is born", keyEvents: ["Sarai's plan", "Hagar conceives", "Conflict arises", "Hagar flees", "Angel's message"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 17, title: "Covenant of Circumcision", description: "God establishes circumcision as the sign of His covenant", keyEvents: ["Name changed to Abraham", "Many nations promise", "Circumcision sign", "Sarah named", "Isaac promised"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 18, title: "Three Visitors to Abraham", description: "Three angels visit Abraham; Isaac's birth promised again", keyEvents: ["Three men appear", "Sarah will have son", "Sarah laughs", "Abraham intercedes", "Sodom's judgment revealed"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 19, title: "Destruction of Sodom and Gomorrah", description: "God destroys Sodom and Gomorrah; Lot escapes", keyEvents: ["Angels enter Sodom", "Lot's hospitality", "Wicked men demand", "City destroyed", "Lot's wife looks back"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 20, title: "Abraham and Abimelech", description: "Abraham deceives Abimelech about Sarah being his wife", keyEvents: ["Journey to Gerar", "Sarah taken", "God warns Abimelech", "Truth revealed", "Restitution made"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 21, title: "Birth of Isaac", description: "Isaac is born; Hagar and Ishmael are sent away", keyEvents: ["Isaac born", "Sarah rejoices", "Ishmael mocks", "Hagar sent away", "Well of oath"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 22, title: "Abraham Offers Isaac", description: "God tests Abraham by asking him to sacrifice Isaac", keyEvents: ["God tests Abraham", "Journey to Moriah", "Isaac asks question", "Angel stops sacrifice", "Ram provided"], difficulty: "easy", estimatedTime: 6 },
  { chapter: 23, title: "Death of Sarah", description: "Sarah dies; Abraham purchases a burial place", keyEvents: ["Sarah dies", "Abraham mourns", "Negotiates for field", "Pays 400 shekels", "Buries Sarah"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 24, title: "Isaac's Wife Found", description: "Abraham's servant finds Rebekah as a wife for Isaac", keyEvents: ["Servant's mission", "Prayer for sign", "Rebekah appears", "Family's consent", "Isaac meets Rebekah"], difficulty: "medium", estimatedTime: 6 },
  { chapter: 25, title: "Abraham's Death; Jacob and Esau Born", description: "Abraham dies; Isaac's twin sons are born", keyEvents: ["Abraham remarries", "Abraham dies", "Isaac and Ishmael bury him", "Twins born", "Esau sells birthright"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 26, title: "Isaac and the Philistines", description: "Isaac deals with famine and conflict over wells", keyEvents: ["Famine in land", "God's promise renewed", "Wife deception", "Well disputes", "Peace covenant"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 27, title: "Jacob Deceives Isaac", description: "Jacob deceives his father and steals Esau's blessing", keyEvents: ["Isaac grows old", "Rebekah's plan", "Jacob disguised", "Blessing stolen", "Esau's anger"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 28, title: "Jacob's Dream at Bethel", description: "Jacob flees and has a dream of a ladder to heaven", keyEvents: ["Jacob flees", "Stops for night", "Ladder dream", "Angels ascending", "God's promise"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 29, title: "Jacob Meets Rachel", description: "Jacob arrives in Haran and meets Rachel at the well", keyEvents: ["Journey to Haran", "Meets Rachel", "Works for Laban", "Marries Leah first", "Then marries Rachel"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 30, title: "Jacob's Children", description: "Jacob's wives compete to give him children", keyEvents: ["Leah bears sons", "Rachel is barren", "Servants bear children", "Mandrakes incident", "Rachel bears Joseph"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 31, title: "Jacob Flees from Laban", description: "Jacob secretly leaves Laban with his family and possessions", keyEvents: ["God tells Jacob to return", "Secret departure", "Rachel steals idols", "Laban pursues", "Covenant made"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 32, title: "Jacob Prepares to Meet Esau", description: "Jacob prepares to meet Esau and wrestles with an angel", keyEvents: ["Angels meet Jacob", "Fears Esau", "Sends gifts ahead", "Wrestles with angel", "Name changed to Israel"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 33, title: "Jacob Meets Esau", description: "Jacob and Esau are reconciled", keyEvents: ["Brothers meet", "Esau shows kindness", "Jacob bows down", "Gifts accepted", "Brothers separate peacefully"], difficulty: "easy", estimatedTime: 4 },
  { chapter: 34, title: "Dinah and the Shechemites", description: "Dinah is violated; her brothers take revenge", keyEvents: ["Dinah defiled", "Shechem wants marriage", "Brothers' deceit", "Circumcision required", "Brothers attack city"], difficulty: "hard", estimatedTime: 5 },
  { chapter: 35, title: "Return to Bethel", description: "Jacob returns to Bethel; Rachel dies giving birth to Benjamin", keyEvents: ["God calls to Bethel", "Idols buried", "Altar built", "Rachel dies", "Benjamin born"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 36, title: "Esau's Descendants", description: "The genealogy of Esau and his descendants in Edom", keyEvents: ["Esau's wives", "Moves to Seir", "Edomite chiefs", "Kings in Edom", "Esau is Edom"], difficulty: "hard", estimatedTime: 3 },
  { chapter: 37, title: "Joseph's Dreams", description: "Joseph dreams of ruling over his brothers; they sell him into slavery", keyEvents: ["Joseph's coat", "Dreams of ruling", "Brothers' hatred", "Sold to Ishmaelites", "Coat bloodied"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 38, title: "Judah and Tamar", description: "Judah's moral failure with his daughter-in-law Tamar", keyEvents: ["Judah marries", "Sons die", "Tamar disguised", "Twins conceived", "Perez born"], difficulty: "hard", estimatedTime: 4 },
  { chapter: 39, title: "Joseph in Potiphar's House", description: "Joseph serves Potiphar but is falsely accused and imprisoned", keyEvents: ["Joseph serves faithfully", "Potiphar's wife tempts", "Joseph refuses", "False accusation", "Prison appointment"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 40, title: "Joseph Interprets Dreams", description: "Joseph interprets dreams for the butler and baker in prison", keyEvents: ["Butler and baker imprisoned", "Both have dreams", "Joseph interprets", "Butler restored", "Baker executed"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 41, title: "Joseph Interprets Pharaoh's Dreams", description: "Joseph interprets Pharaoh's dreams and becomes second in command", keyEvents: ["Pharaoh's dreams", "No one can interpret", "Joseph called", "Seven years plenty/famine", "Made ruler"], difficulty: "easy", estimatedTime: 6 },
  { chapter: 42, title: "Joseph's Brothers Come to Egypt", description: "Jacob's sons come to Egypt to buy grain during the famine", keyEvents: ["Famine severe", "Brothers go to Egypt", "Joseph recognizes them", "Accused of spying", "Simeon kept hostage"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 43, title: "Second Journey to Egypt", description: "The brothers return to Egypt with Benjamin", keyEvents: ["Must bring Benjamin", "Judah's guarantee", "Double money taken", "Feast with Joseph", "Benjamin favored"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 44, title: "Joseph's Silver Cup", description: "Joseph tests his brothers by placing his cup in Benjamin's sack", keyEvents: ["Cup in Benjamin's sack", "Brothers searched", "Return to city", "Judah's plea", "Offers to stay"], difficulty: "medium", estimatedTime: 5 },
  { chapter: 45, title: "Joseph Reveals Himself", description: "Joseph reveals his identity to his brothers", keyEvents: ["Can't control himself", "I am Joseph", "Brothers terrified", "God sent me", "Bring father down"], difficulty: "easy", estimatedTime: 5 },
  { chapter: 46, title: "Jacob Goes to Egypt", description: "Jacob and his entire family move to Egypt", keyEvents: ["God speaks to Jacob", "Promises to go down", "70 souls total", "Joseph meets father", "Settled in Goshen"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 47, title: "Jacob Blesses Pharaoh", description: "Jacob meets Pharaoh and the family settles in Goshen", keyEvents: ["Jacob meets Pharaoh", "Blesses Pharaoh", "Famine continues", "Joseph manages crisis", "Jacob lives 17 more years"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 48, title: "Jacob Blesses Joseph's Sons", description: "Jacob adopts and blesses Ephraim and Manasseh", keyEvents: ["Jacob ill", "Joseph brings sons", "Cross-handed blessing", "Ephraim before Manasseh", "Future greatness promised"], difficulty: "medium", estimatedTime: 4 },
  { chapter: 49, title: "Jacob's Final Blessings", description: "Jacob gives prophetic blessings to each of his twelve sons", keyEvents: ["Reuben loses firstborn rights", "Judah gets scepter", "Each son blessed", "Prophetic words", "Burial instructions"], difficulty: "hard", estimatedTime: 6 },
  { chapter: 50, title: "Death of Jacob and Joseph", description: "Jacob dies and is buried; Joseph reassures his brothers and dies", keyEvents: ["Jacob dies", "Embalmed in Egypt", "Buried in Canaan", "Brothers fear Joseph", "Joseph dies at 110"], difficulty: "medium", estimatedTime: 5 }
];

export default function GenesisChaptersPage() {
  const genesisBook = getBibleBook('genesis');
  const chapters = getBookChapterRange('genesis');

  if (!genesisBook) {
    return <div>Book not found</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-amber-50 to-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Genesis Chapter Quizzes
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            Explore each chapter of Genesis with focused quizzes covering key events, characters, and themes
          </p>
          
          {/* Quick Stats */}
          <div className="bg-white rounded-xl shadow-lg p-6 max-w-4xl mx-auto mb-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-600">{genesisBook.chapters}</div>
                <div className="text-sm text-gray-600">Chapters</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600">{genesisBook.chapters * 18}</div>
                <div className="text-sm text-gray-600">Questions</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600">{Math.round(genesisBook.chapters * 4.5)}</div>
                <div className="text-sm text-gray-600">Minutes</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-amber-600 capitalize">{genesisBook.difficulty}</div>
                <div className="text-sm text-gray-600">Level</div>
              </div>
            </div>
          </div>

          {/* Main Genesis Quiz CTA */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-6 mb-8">
            <h2 className="text-2xl font-bold mb-2">Complete Genesis Quiz</h2>
            <p className="text-blue-100 mb-4">
              Test your knowledge of the entire book with our comprehensive 50-question quiz
            </p>
            <Link 
              href="/genesis-quiz" 
              className="inline-block bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Take Full Genesis Quiz
            </Link>
          </div>
        </div>

        {/* Chapter Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {GENESIS_CHAPTERS.map((chapterData) => (
            <Link key={chapterData.chapter} href={`/genesis-${chapterData.chapter}-quiz`}>
              <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 h-full">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-gray-900">
                    Chapter {chapterData.chapter}
                  </h3>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      chapterData.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                      chapterData.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {chapterData.difficulty}
                    </span>
                    <span className="text-sm text-gray-500">
                      ~{chapterData.estimatedTime}min
                    </span>
                  </div>
                </div>
                
                <h4 className="text-lg font-semibold text-blue-600 mb-2">
                  {chapterData.title}
                </h4>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {chapterData.description}
                </p>
                
                <div className="mb-4">
                  <h5 className="text-sm font-semibold text-gray-700 mb-2">Key Events:</h5>
                  <div className="flex flex-wrap gap-1">
                    {chapterData.keyEvents.slice(0, 3).map((event, index) => (
                      <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                        {event}
                      </span>
                    ))}
                    {chapterData.keyEvents.length > 3 && (
                      <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                        +{chapterData.keyEvents.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <span className="text-sm text-gray-500">
                    Genesis {chapterData.chapter}
                  </span>
                  <div className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium group-hover:bg-blue-700 transition-colors">
                    Take Quiz
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Navigation Links */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Continue Your Bible Study Journey</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <Link href="/genesis-quiz" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              📖 Complete Genesis Quiz
            </Link>
            <Link href="/exodus-chapters" className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              ➡️ Exodus Chapters
            </Link>
            <Link href="/abraham-quiz" className="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors">
              👤 Abraham Quiz
            </Link>
            <Link href="/bible-quizzes" className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              🏠 All Bible Quizzes
            </Link>
          </div>
        </div>

        {/* Study Resources */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div className="relative">
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <Image
                  src="/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png"
                  alt="Hands opening the Bible"
                  width={500}
                  height={300}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Master the Book of Beginnings
              </h2>
              <p className="text-gray-600 mb-6">
                Genesis is the foundation of all Scripture, containing the origins of creation, humanity, 
                sin, and God's plan of redemption. Each chapter builds upon the previous, creating a 
                comprehensive narrative of God's relationship with mankind.
              </p>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-gray-700 text-sm">
                    <strong>Chapters 1-11:</strong> Universal history - Creation, Fall, Flood, and Babel
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-gray-700 text-sm">
                    <strong>Chapters 12-36:</strong> Patriarchal history - Abraham, Isaac, and Jacob
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-gray-700 text-sm">
                    <strong>Chapters 37-50:</strong> Joseph's story - From slavery to rulership in Egypt
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}