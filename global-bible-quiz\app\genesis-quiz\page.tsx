'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getBibleBook } from '@/lib/data/bible-books';

// Comprehensive Genesis Quiz - 50 questions covering the entire book (LINEAR FORMAT)
const GENESIS_QUIZ_QUESTIONS = [
  {
    id: 1,
    question: "What did God create on the first day?",
    options: ["Light", "The heavens and earth", "Plants", "Animals"],
    correctAnswer: 0,
    explanation: "On the first day, <PERSON> said 'Let there be light,' and there was light.",
    verseReference: "Genesis 1:3-5",
    difficulty: "easy"
  },
  {
    id: 2,
    question: "In whose image was man created?",
    options: ["Angels", "Animals", "God's image", "No specific image"],
    correctAnswer: 2,
    explanation: "God created mankind in His own image, in the image of God He created them.",
    verseReference: "Genesis 1:27",
    difficulty: "easy"
  },
  {
    id: 3,
    question: "What was the name of the tree in the middle of the Garden of Eden?",
    options: ["Tree of Life", "Tree of Knowledge of Good and Evil", "Both trees were there", "Tree of Wisdom"],
    correctAnswer: 2,
    explanation: "Both the Tree of Life and the Tree of Knowledge of Good and Evil were in the midst of the garden.",
    verseReference: "Genesis 2:9",
    difficulty: "medium"
  },
  {
    id: 4,
    question: "Who was the first woman?",
    options: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
    correctAnswer: 2,
    explanation: "<PERSON> called his wife's name <PERSON>, because she was the mother of all living.",
    verseReference: "<PERSON> 3:20",
    difficulty: "easy"
  },
  {
    id: 5,
    question: "What did God make for <PERSON> and <PERSON> to cover their nakedness?",
    options: ["Fig leaves", "Animal skins", "Woven cloth", "Nothing"],
    correctAnswer: 1,
    explanation: "The Lord God made clothing of skin for Adam and his wife and clothed them.",
    verseReference: "Genesis 3:21",
    difficulty: "medium"
  },
  {
    id: 6,
    question: "What was Cain's occupation?",
    options: ["Shepherd", "Farmer", "Hunter", "Builder"],
    correctAnswer: 1,
    explanation: "Cain was a tiller of the ground (farmer).",
    verseReference: "Genesis 4:2",
    difficulty: "easy"
  },
  {
    id: 7,
    question: "How old was Noah when the flood began?",
    options: ["500 years", "600 years", "700 years", "950 years"],
    correctAnswer: 1,
    explanation: "Noah was six hundred years old when the floodwaters came on the earth.",
    verseReference: "Genesis 7:6",
    difficulty: "medium"
  },
  {
    id: 8,
    question: "How many of each clean animal did Noah take into the ark?",
    options: ["Two", "Seven pairs", "One pair", "Fourteen"],
    correctAnswer: 1,
    explanation: "Of every clean animal you shall take seven pairs, a male and his female.",
    verseReference: "Genesis 7:2",
    difficulty: "hard"
  },
  {
    id: 9,
    question: "What sign did God give as a covenant after the flood?",
    options: ["A dove", "A rainbow", "A mountain", "A star"],
    correctAnswer: 1,
    explanation: "God set His rainbow in the cloud as a sign of the covenant between Him and the earth.",
    verseReference: "Genesis 9:13",
    difficulty: "easy"
  },
  {
    id: 10,
    question: "What was the name of the tower built in the land of Shinar?",
    options: ["Tower of David", "Tower of Babel", "Tower of Strength", "Tower of Zion"],
    correctAnswer: 1,
    explanation: "The Lord confused the language of all the earth there, so its name was called Babel.",
    verseReference: "Genesis 11:9",
    difficulty: "easy"
  },
  {
    id: 11,
    question: "From which city was Abraham called to leave?",
    options: ["Babylon", "Ur of the Chaldeans", "Haran", "Damascus"],
    correctAnswer: 1,
    explanation: "The God of glory appeared to our father Abraham when he was in Mesopotamia, before he lived in Haran.",
    verseReference: "Genesis 11:31, Acts 7:2",
    difficulty: "medium"
  },
  {
    id: 12,
    question: "How old was Abraham when Isaac was born?",
    options: ["75 years", "99 years", "100 years", "120 years"],
    correctAnswer: 2,
    explanation: "Abraham was one hundred years old when his son Isaac was born to him.",
    verseReference: "Genesis 21:5",
    difficulty: "medium"
  },
  {
    id: 13,
    question: "Where did Abraham go to sacrifice Isaac?",
    options: ["Mount Sinai", "Mount Moriah", "Mount Carmel", "Mount Horeb"],
    correctAnswer: 1,
    explanation: "God said, 'Go to the land of Moriah, and offer him there as a burnt offering.'",
    verseReference: "Genesis 22:2",
    difficulty: "medium"
  },
  {
    id: 14,
    question: "Who was Isaac's wife?",
    options: ["Sarah", "Rachel", "Rebekah", "Leah"],
    correctAnswer: 2,
    explanation: "Isaac took Rebekah as his wife, and he loved her.",
    verseReference: "Genesis 24:67",
    difficulty: "easy"
  },
  {
    id: 15,
    question: "Which son did Isaac love more?",
    options: ["Jacob", "Esau", "He loved them equally", "Neither"],
    correctAnswer: 1,
    explanation: "Isaac loved Esau because he ate of his game, but Rebekah loved Jacob.",
    verseReference: "Genesis 25:28",
    difficulty: "easy"
  },
  {
    id: 16,
    question: "For what did Esau sell his birthright?",
    options: ["Money", "Land", "A bowl of stew", "Cattle"],
    correctAnswer: 2,
    explanation: "Esau sold his birthright to Jacob for bread and stew of lentils.",
    verseReference: "Genesis 25:34",
    difficulty: "easy"
  },
  {
    id: 17,
    question: "What new name did God give to Jacob?",
    options: ["Abraham", "Israel", "Isaac", "Joseph"],
    correctAnswer: 1,
    explanation: "Your name shall no longer be called Jacob, but Israel; for you have struggled with God and with men, and have prevailed.",
    verseReference: "Genesis 32:28",
    difficulty: "easy"
  },
  {
    id: 18,
    question: "How many sons did Jacob have?",
    options: ["10", "11", "12", "13"],
    correctAnswer: 2,
    explanation: "Jacob had twelve sons, who became the heads of the twelve tribes of Israel.",
    verseReference: "Genesis 35:22",
    difficulty: "easy"
  },
  {
    id: 19,
    question: "Who was Jacob's favorite son?",
    options: ["Reuben", "Joseph", "Benjamin", "Judah"],
    correctAnswer: 1,
    explanation: "Israel loved Joseph more than all his children, because he was the son of his old age.",
    verseReference: "Genesis 37:3",
    difficulty: "easy"
  },
  {
    id: 20,
    question: "What did Joseph's brothers tell their father happened to Joseph?",
    options: ["He ran away", "He was killed by a wild animal", "He was kidnapped", "He fell into a pit"],
    correctAnswer: 1,
    explanation: "They said, 'A wild beast has devoured him. Without doubt Joseph is torn to pieces.'",
    verseReference: "Genesis 37:33",
    difficulty: "medium"
  },
  {
    id: 21,
    question: "To which country was Joseph sold as a slave?",
    options: ["Babylon", "Egypt", "Assyria", "Canaan"],
    correctAnswer: 1,
    explanation: "The Midianites sold Joseph to Potiphar, an officer of Pharaoh and captain of the guard, an Egyptian.",
    verseReference: "Genesis 37:36",
    difficulty: "easy"
  },
  {
    id: 22,
    question: "Who falsely accused Joseph?",
    options: ["Pharaoh", "Potiphar's wife", "His brothers", "The chief butler"],
    correctAnswer: 1,
    explanation: "Potiphar's wife spoke to Joseph day by day, but he refused to lie with her. She then falsely accused him.",
    verseReference: "Genesis 39:7-18",
    difficulty: "medium"
  },
  {
    id: 23,
    question: "What did the seven fat cows represent in Pharaoh's dream?",
    options: ["Seven years of war", "Seven years of plenty", "Seven years of famine", "Seven plagues"],
    correctAnswer: 1,
    explanation: "The seven good cows are seven years of plenty that will come throughout all the land of Egypt.",
    verseReference: "Genesis 41:26",
    difficulty: "medium"
  },
  {
    id: 24,
    question: "How old was Joseph when he stood before Pharaoh?",
    options: ["25 years", "30 years", "35 years", "40 years"],
    correctAnswer: 1,
    explanation: "Joseph was thirty years old when he stood before Pharaoh king of Egypt.",
    verseReference: "Genesis 41:46",
    difficulty: "hard"
  },
  {
    id: 25,
    question: "What did Joseph hide in Benjamin's sack?",
    options: ["Money", "His silver cup", "Grain", "Jewelry"],
    correctAnswer: 1,
    explanation: "Put my cup, the silver cup, in the mouth of the sack of the youngest.",
    verseReference: "Genesis 44:2",
    difficulty: "medium"
  },
  {
    id: 26,
    question: "How many people of Jacob's family went to Egypt?",
    options: ["70", "75", "60", "80"],
    correctAnswer: 0,
    explanation: "All the persons of the house of Jacob who went to Egypt were seventy.",
    verseReference: "Genesis 46:27",
    difficulty: "hard"
  },
  {
    id: 27,
    question: "In which region of Egypt did Jacob's family settle?",
    options: ["Memphis", "Goshen", "Thebes", "Alexandria"],
    correctAnswer: 1,
    explanation: "Joseph settled his father and his brothers in the land of Egypt, in the best of the land, in the land of Rameses, as Pharaoh had commanded.",
    verseReference: "Genesis 47:11",
    difficulty: "medium"
  },
  {
    id: 28,
    question: "How old was Jacob when he died?",
    options: ["120 years", "137 years", "147 years", "157 years"],
    correctAnswer: 2,
    explanation: "Jacob lived in the land of Egypt seventeen years. So the length of Jacob's life was one hundred and forty-seven years.",
    verseReference: "Genesis 47:28",
    difficulty: "hard"
  },
  {
    id: 29,
    question: "Which son did Jacob bless as the one through whom the scepter would come?",
    options: ["Reuben", "Joseph", "Judah", "Benjamin"],
    correctAnswer: 2,
    explanation: "The scepter shall not depart from Judah, nor a lawgiver from between his feet, until Shiloh comes.",
    verseReference: "Genesis 49:10",
    difficulty: "medium"
  },
  {
    id: 30,
    question: "How old was Joseph when he died?",
    options: ["100 years", "110 years", "120 years", "130 years"],
    correctAnswer: 1,
    explanation: "Joseph died, being one hundred and ten years old.",
    verseReference: "Genesis 50:26",
    difficulty: "hard"
  },
  {
    id: 31,
    question: "What did God breathe into Adam to make him a living soul?",
    options: ["The breath of life", "His spirit", "Divine essence", "Holy wind"],
    correctAnswer: 0,
    explanation: "The Lord God formed man of the dust of the ground, and breathed into his nostrils the breath of life; and man became a living being.",
    verseReference: "Genesis 2:7",
    difficulty: "medium"
  },
  {
    id: 32,
    question: "What was the punishment for eating from the forbidden tree?",
    options: ["Banishment", "Death", "Sickness", "Blindness"],
    correctAnswer: 1,
    explanation: "Of the tree of the knowledge of good and evil you shall not eat, for in the day that you eat of it you shall surely die.",
    verseReference: "Genesis 2:17",
    difficulty: "easy"
  },
  {
    id: 33,
    question: "Who was the father of those who dwell in tents and have livestock?",
    options: ["Cain", "Abel", "Jabal", "Jubal"],
    correctAnswer: 2,
    explanation: "Jabal was the father of those who dwell in tents and have livestock.",
    verseReference: "Genesis 4:20",
    difficulty: "hard"
  },
  {
    id: 34,
    question: "How long did it rain during the flood?",
    options: ["40 days and nights", "30 days and nights", "50 days and nights", "60 days and nights"],
    correctAnswer: 0,
    explanation: "The rain was on the earth forty days and forty nights.",
    verseReference: "Genesis 7:12",
    difficulty: "easy"
  },
  {
    id: 35,
    question: "What did the dove bring back to Noah?",
    options: ["A twig", "An olive leaf", "A flower", "Nothing"],
    correctAnswer: 1,
    explanation: "The dove came to him in the evening, and behold, a freshly plucked olive leaf was in her mouth.",
    verseReference: "Genesis 8:11",
    difficulty: "easy"
  },
  {
    id: 36,
    question: "What was Abram's name before God changed it?",
    options: ["Abraham", "Abram", "Ibrahim", "Avram"],
    correctAnswer: 1,
    explanation: "Abram was his original name before God changed it to Abraham.",
    verseReference: "Genesis 17:5",
    difficulty: "easy"
  },
  {
    id: 37,
    question: "Who was Hagar?",
    options: ["Sarah's sister", "Sarah's servant", "Abraham's relative", "A neighbor"],
    correctAnswer: 1,
    explanation: "Hagar was Sarah's Egyptian maidservant.",
    verseReference: "Genesis 16:1",
    difficulty: "easy"
  },
  {
    id: 38,
    question: "What was the name of Hagar's son?",
    options: ["Isaac", "Ishmael", "Jacob", "Esau"],
    correctAnswer: 1,
    explanation: "Hagar bore Abram a son; and Abram named his son, whom Hagar bore, Ishmael.",
    verseReference: "Genesis 16:15",
    difficulty: "easy"
  },
  {
    id: 39,
    question: "What cities did God destroy with fire and brimstone?",
    options: ["Babylon and Nineveh", "Sodom and Gomorrah", "Tyre and Sidon", "Memphis and Thebes"],
    correctAnswer: 1,
    explanation: "The Lord rained brimstone and fire on Sodom and Gomorrah, from the Lord out of the heavens.",
    verseReference: "Genesis 19:24",
    difficulty: "easy"
  },
  {
    id: 40,
    question: "What happened to Lot's wife?",
    options: ["She died in the fire", "She turned into a pillar of salt", "She was saved", "She escaped to the mountains"],
    correctAnswer: 1,
    explanation: "Lot's wife looked back behind him, and she became a pillar of salt.",
    verseReference: "Genesis 19:26",
    difficulty: "easy"
  },
  {
    id: 41,
    question: "Through which son was the promise to be fulfilled?",
    options: ["Ishmael", "Isaac", "Both sons", "Neither son"],
    correctAnswer: 1,
    explanation: "In Isaac your seed shall be called.",
    verseReference: "Genesis 21:12",
    difficulty: "medium"
  },
  {
    id: 42,
    question: "Where did Abraham purchase a burial place?",
    options: ["Hebron", "Bethel", "Beersheba", "Mamre"],
    correctAnswer: 0,
    explanation: "Abraham purchased the field and the cave that is in it from the sons of Heth.",
    verseReference: "Genesis 23:20",
    difficulty: "medium"
  },
  {
    id: 43,
    question: "Who helped Isaac dig wells?",
    options: ["His servants", "The Philistines", "Abraham's men", "Local farmers"],
    correctAnswer: 0,
    explanation: "Isaac's servants dug in the valley, and found a well of running water there.",
    verseReference: "Genesis 26:19",
    difficulty: "medium"
  },
  {
    id: 44,
    question: "What did Jacob see in his dream at Bethel?",
    options: ["Angels", "A ladder", "God's throne", "Heaven opened"],
    correctAnswer: 1,
    explanation: "He dreamed, and behold, a ladder was set up on the earth, and its top reached to heaven; and there the angels of God were ascending and descending on it.",
    verseReference: "Genesis 28:12",
    difficulty: "easy"
  },
  {
    id: 45,
    question: "How many years did Jacob work for Rachel?",
    options: ["7 years", "14 years", "21 years", "28 years"],
    correctAnswer: 1,
    explanation: "Jacob served seven years for Rachel, and they seemed only a few days to him because of the love he had for her. Then he worked another seven years.",
    verseReference: "Genesis 29:20, 29:30",
    difficulty: "medium"
  },
  {
    id: 46,
    question: "What was the name of Joseph's Egyptian wife?",
    options: ["Asenath", "Potipherah", "Zipporah", "Hagar"],
    correctAnswer: 0,
    explanation: "Pharaoh gave Joseph Asenath, the daughter of Poti-Pherah priest of On, as wife.",
    verseReference: "Genesis 41:45",
    difficulty: "hard"
  },
  {
    id: 47,
    question: "What were the names of Joseph's two sons?",
    options: ["Ephraim and Manasseh", "Gad and Asher", "Dan and Naphtali", "Issachar and Zebulun"],
    correctAnswer: 0,
    explanation: "Joseph called the name of the firstborn Manasseh and the name of the second he called Ephraim.",
    verseReference: "Genesis 41:51-52",
    difficulty: "medium"
  },
  {
    id: 48,
    question: "What did Joseph command to be put in each brother's sack?",
    options: ["Silver", "Their money", "Extra grain", "Gifts"],
    correctAnswer: 1,
    explanation: "Joseph gave command to fill their sacks with grain, to restore every man's money to his sack.",
    verseReference: "Genesis 42:25",
    difficulty: "medium"
  },
  {
    id: 49,
    question: "What did Jacob bless Joseph's sons with?",
    options: ["Wealth", "The blessing of Abraham", "Leadership", "Long life"],
    correctAnswer: 1,
    explanation: "By you Israel will bless, saying, 'May God make you as Ephraim and as Manasseh!' And thus he set Ephraim before Manasseh.",
    verseReference: "Genesis 48:20",
    difficulty: "hard"
  },
  {
    id: 50,
    question: "Where was Joseph buried?",
    options: ["Egypt", "Canaan", "Shechem", "Hebron"],
    correctAnswer: 2,
    explanation: "The bones of Joseph, which the children of Israel had brought up out of Egypt, they buried at Shechem.",
    verseReference: "Joshua 24:32",
    difficulty: "hard"
  }
];

export default function GenesisQuizPage() {
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>(new Array(GENESIS_QUIZ_QUESTIONS.length).fill(-1));
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);

  const genesisBook = getBibleBook('genesis');

  const handleAnswerSelect = (questionIndex: number, answerIndex: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[questionIndex] = answerIndex;
    setSelectedAnswers(newAnswers);
  };

  const handleSubmitQuiz = () => {
    // Check if all questions are answered
    const unansweredQuestions = selectedAnswers.filter(answer => answer === -1).length;
    if (unansweredQuestions > 0) {
      alert(`Please answer all questions. You have ${unansweredQuestions} unanswered questions.`);
      return;
    }

    // Calculate score
    let correct = 0;
    GENESIS_QUIZ_QUESTIONS.forEach((question, index) => {
      if (selectedAnswers[index] === question.correctAnswer) {
        correct++;
      }
    });
    setScore(correct);
    setShowResults(true);
    
    // Scroll to results section
    setTimeout(() => {
      const resultsElement = document.getElementById('quiz-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  const getScoreMessage = () => {
    const percentage = Math.round((score / GENESIS_QUIZ_QUESTIONS.length) * 100);
    if (percentage >= 90) return "Outstanding! You're a Genesis scholar!";
    if (percentage >= 80) return "Excellent! You know Genesis well!";
    if (percentage >= 70) return "Good job! Keep studying to improve!";
    if (percentage >= 60) return "Not bad! Review Genesis for better results.";
    return "Keep learning! Study Genesis more closely.";
  };

  const resetQuiz = () => {
    setSelectedAnswers(new Array(GENESIS_QUIZ_QUESTIONS.length).fill(-1));
    setShowResults(false);
    setScore(0);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mb-4">
            <Link href="/bible-quizzes" className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              ← Back to Bible Quizzes
            </Link>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Complete Genesis Quiz</h1>
          <p className="text-lg text-gray-600 mb-6">
            Test your knowledge of the entire book of Genesis - from Creation to Joseph's story
          </p>
          
          {/* Quiz Instructions */}
          <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
            <p className="text-gray-700 mb-4">
              Answer all questions below and click "Submit Quiz and See Results" when you're finished.
              This comprehensive quiz covers the entire book of Genesis with 50 carefully crafted questions.
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
              <span>📊 {GENESIS_QUIZ_QUESTIONS.length} Questions</span>
              <span>⏱️ ~25-30 Minutes</span>
              <span>📖 Genesis 1-50</span>
              <span>🎯 All Difficulty Levels</span>
            </div>
          </div>
        </div>

        {/* All Questions - Linear Format */}
        <div className="space-y-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quiz Questions</h2>
          
          {GENESIS_QUIZ_QUESTIONS.map((question, questionIndex) => (
            <div key={question.id} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="mb-4">
                <div className="flex items-center gap-2 mb-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    question.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                    question.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {question.difficulty}
                  </span>
                  <span className="text-sm text-gray-500">{question.verseReference}</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {questionIndex + 1}. {question.question}
                </h3>
              </div>

              <div className="space-y-2">
                {question.options.map((option, optionIndex) => (
                  <button
                    key={optionIndex}
                    onClick={() => handleAnswerSelect(questionIndex, optionIndex)}
                    className={`w-full text-left p-3 rounded-lg border-2 transition-all duration-200 ${
                      selectedAnswers[questionIndex] === optionIndex
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                        selectedAnswers[questionIndex] === optionIndex
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}>
                        {selectedAnswers[questionIndex] === optionIndex && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span className="text-sm font-medium">
                        {String.fromCharCode(65 + optionIndex)}. {option}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Submit Button */}
        <div className="text-center mb-12">
          <button
            onClick={handleSubmitQuiz}
            className="px-8 py-4 bg-blue-600 text-white text-lg font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
          >
            Submit Quiz and See Results
          </button>
          <p className="text-sm text-gray-600 mt-2">
            Make sure you've answered all {GENESIS_QUIZ_QUESTIONS.length} questions before submitting
          </p>
        </div>

        {/* Results Section */}
        {showResults && (
          <div id="quiz-results" className="space-y-8">
            {/* Score Display */}
            <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Your Genesis Quiz Results</h2>
              <div className="text-6xl font-bold text-blue-600 mb-4">
                {Math.round((score / GENESIS_QUIZ_QUESTIONS.length) * 100)}%
              </div>
              <p className="text-xl text-gray-700 mb-4">
                You scored {score} out of {GENESIS_QUIZ_QUESTIONS.length} questions correctly
              </p>
              <p className="text-lg text-blue-600 font-semibold">{getScoreMessage()}</p>
            </div>

            {/* Detailed Results */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Question Breakdown</h3>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {GENESIS_QUIZ_QUESTIONS.map((question, index) => {
                  const isCorrect = selectedAnswers[index] === question.correctAnswer;
                  return (
                    <div key={question.id} className={`p-4 rounded-lg border-2 ${
                      isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                    }`}>
                      <div className="flex items-start gap-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                          isCorrect ? 'bg-green-500' : 'bg-red-500'
                        }`}>
                          {isCorrect ? '✓' : '✗'}
                        </div>
                        <div className="flex-1">
                          <p className="font-semibold text-gray-900 mb-2">
                            {index + 1}. {question.question}
                          </p>
                          <p className="text-sm text-gray-600 mb-1">
                            Your answer: {question.options[selectedAnswers[index]]}
                          </p>
                          {!isCorrect && (
                            <p className="text-sm text-green-700 mb-1">
                              Correct answer: {question.options[question.correctAnswer]}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 italic">
                            {question.explanation} ({question.verseReference})
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* MANDATORY Internal Links Section */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">Continue Your Bible Study Journey</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <Link href="/genesis-chapters" className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  📖 Study Individual Genesis Chapters
                </Link>
                <Link href="/exodus-quiz" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  → Exodus Quiz
                </Link>
                <Link href="/abraham-quiz" className="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors">
                  👤 Abraham Character Study
                </Link>
                <Link href="/isaac-quiz" className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                  👤 Isaac Character Study
                </Link>
                <Link href="/jacob-quiz" className="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors">
                  👤 Jacob/Israel Character Study
                </Link>
                <Link href="/joseph-quiz" className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                  👤 Joseph Character Study
                </Link>
                <Link href="/creation-quiz" className="inline-flex items-center px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors">
                  🌍 Creation Theme Quiz
                </Link>
                <Link href="/covenant-quiz" className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                  📜 Covenant Theme Quiz
                </Link>
                <Link href="/bible-quizzes" className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                  🏠 Browse All Bible Quizzes
                </Link>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="text-center space-x-4">
              <button 
                onClick={resetQuiz}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Retake Quiz
              </button>
              <Link 
                href="/genesis-chapters" 
                className="inline-block px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
              >
                Study Genesis Chapters
              </Link>
            </div>
          </div>
        )}

        {/* Study Resources */}
        <div className="bg-white rounded-xl shadow-lg p-6 mt-12">
          <div className="grid lg:grid-cols-2 gap-6 items-center">
            <div className="relative">
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <Image
                  src="/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png"
                  alt="Man in deep prayer with divine light"
                  width={400}
                  height={250}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Master the Foundation of Scripture
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                Genesis is the foundation of all Scripture, revealing God's character, His plan for 
                humanity, and the origins of His covenant relationship. This book contains some of 
                the most well-known stories and fundamental truths of the Christian faith.
              </p>
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-sm text-gray-700">Creation and the beginning of all things</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-sm text-gray-700">The patriarchs: Abraham, Isaac, Jacob, and Joseph</p>
                </div>
                <div className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></div>
                  <p className="text-sm text-gray-700">God's covenant promises and faithfulness</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}