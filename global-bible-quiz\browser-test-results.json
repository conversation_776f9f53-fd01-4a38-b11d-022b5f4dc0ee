{"timestamp": "2025-07-31T04:21:35.565Z", "accessiblePort": 3000, "pageContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"/><link rel=\"stylesheet\" href=\"/_next/static/css/app/layout.css?v=1753935695760\" data-precedence=\"next_static/css/app/layout.css\"/><link rel=\"preload\" as=\"script\" fetchPriority=\"low\" href=\"/_next/static/chunks/webpack.js?v=1753935695760\"/><script src=\"/_next/static/chunks/main-app.js?v=1753935695760\" async=\"\"></script><script src=\"/_next/static/chunks/app-pages-internals.js\" async=\"\"></script><script src=\"/_next/static/chunks/app/page.js\" async=\"\"></script><script src=\"/_next/static/chunks/app/layout.js\" async=\"\"></script><link rel=\"manifest\" href=\"/manifest.json\"/><meta name=\"theme-color\" content=\"#2563eb\"/><meta name=\"mobile-web-app-capable\" content=\"yes\"/><meta name=\"apple-mobile-web-app-capable\" content=\"yes\"/><meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\"/><meta name=\"apple-mobile-web-app-title\" content=\"Bible Quiz\"/><link rel=\"apple-touch-icon\" href=\"/icon-192x192.png\"/><title>Bible Quiz - Test Your Biblical Knowledge | BibleQuiz</title><meta name=\"description\" content=\"Test your Bible knowledge with comprehensive quizzes covering all 66 books. Interactive questions with detailed explanations and scripture references. Perfect for Bible study!\"/><meta name=\"keywords\" content=\"bible quiz,scripture test,biblical knowledge,bible study,christian quiz\"/><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" sizes=\"16x16\"/><script type=\"application/ld+json\">{\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"WebSite\",\n  \"name\": \"Bible Quiz - Test Your Biblical Knowledge\",\n  \"alternateName\": \"BibleQuiz\",\n  \"url\": \"https://bibleQuiz.com\",\n  \"description\": \"Interactive Bible quizzes covering all 66 books with detailed explanations and scripture references. Perfect for Bible study, Sunday school, and personal spiritual growth.\",\n  \"potentialAction\": {\n    \"@type\": \"SearchAction\",\n    \"target\": \"https://bibleQuiz.com/search?q={search_term_string}\",\n    \"query-input\": \"required name=search_term_string\"\n  },\n  \"publisher\": {\n    \"@type\": \"Organization\",\n    \"name\": \"Bible Quiz\",\n    \"url\": \"https://bibleQuiz.com\",\n    \"logo\": {\n      \"@type\": \"ImageObject\",\n      \"url\": \"https://bibleQuiz.com/icon-512x512.svg\",\n      \"width\": 512,\n      \"height\": 512\n    }\n  },\n  \"sameAs\": []\n}</script><script src=\"/_next/static/chunks/polyfills.js\" noModule=\"\"></script></head><body class=\"__variable_1e4310 __variable_c3aa02 antialiased bg-gray-50\"><a href=\"#main-content\" class=\"skip-link\">Skip to main content</a><a href=\"#navigation\" class=\"skip-link\">Skip to navigation</a><nav class=\"bg-white shadow-lg border-b border-blue-100\"><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><div class=\"flex justify-between items-center h-16\"><a class=\"flex items-center space-x-2\" href=\"/\"><svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"text-blue-600\"><path d=\"M4 2C3.45 2 3 2.45 3 3V21C3 21.55 3.45 22 4 22H20C20.55 22 21 21.55 21 21V3C21 2.45 20.55 2 20 2H4ZM6 4H18V20H6V4ZM8 6V8H16V6H8ZM8 10V12H16V10H8ZM8 14V16H13V14H8Z\" fill=\"currentColor\"></path></svg><span class=\"text-xl font-bold text-gray-900\">BibleQuiz</span></a><div class=\"hidden md:flex items-center space-x-8\"><div class=\"relative group\"><button class=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2\"><span>Bible Quizzes</span><svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"transform rotate-90\"><path d=\"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\" fill=\"currentColor\"></path></svg></button><div class=\"absolute top-full left-0 mt-1 w-64 bg-white shadow-lg rounded-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\"><div class=\"py-2\"><a class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\" href=\"/bible-quizzes\">All Bible Quizzes</a><a class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\" href=\"/old-testament-quizzes\">Old Testament</a><a class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\" href=\"/new-testament-quizzes\">New Testament</a><a class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\" href=\"/genesis-chapters\">Genesis Chapters</a><a class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\" href=\"/matthew-quiz\">Matthew Quiz</a></div></div></div><a class=\"text-gray-700 hover:text-blue-600 px-3 py-2\" href=\"/bible-characters\">Characters</a><a class=\"text-gray-700 hover:text-blue-600 px-3 py-2\" href=\"/study-guides\">Study Guides</a><a class=\"text-gray-700 hover:text-blue-600 px-3 py-2\" href=\"/about\">About</a></div><button class=\"md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100\" aria-label=\"Toggle mobile menu\"><svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"\"><path d=\"M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z\" fill=\"currentColor\"></path></svg></button></div></div></nav><button class=\"fixed top-4 right-4 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\" aria-label=\"Open accessibility settings\" aria-expanded=\"false\"><svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path d=\"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zM4 10a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zM14 10a1 1 0 011-1h1a1 1 0 110 2h-1a1 1 0 01-1-1zM10 16a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM6.343 6.343a1 1 0 011.414 0l.707.707a1 1 0 11-1.414 1.414l-.707-.707a1 1 0 010-1.414zM12.828 12.828a1 1 0 011.414 0l.707.707a1 1 0 11-1.414 1.414l-.707-.707a1 1 0 010-1.414zM13.657 6.343a1 1 0 010 1.414l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 0zM7.172 12.828a1 1 0 010 1.414l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 0z\"></path></svg></button><main id=\"main-content\" class=\"min-h-screen\" role=\"main\"><div class=\"min-h-screen\"><section class=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20\"><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\"><div class=\"flex justify-center mb-6\"><svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"text-blue-200\"><path d=\"M4 2C3.45 2 3 2.45 3 3V21C3 21.55 3.45 22 4 22H20C20.55 22 21 21.55 21 21V3C21 2.45 20.55 2 20 2H4ZM6 4H18V20H6V4ZM8 6V8H16V6H8ZM8 10V12H16V10H8ZM8 14V16H13V14H8Z\" fill=\"currentColor\"></path></svg></div><h1 class=\"text-4xl md:text-6xl font-bold mb-6\">Test Your Biblical Knowledge</h1><p class=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto\">Interactive Bible quizzes covering all 66 books with detailed explanations and scripture references. Perfect for Bible study, Sunday school, and personal growth.</p><div class=\"flex flex-col sm:flex-row gap-4 justify-center\"><a class=\"bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1\" href=\"/genesis-1-quiz\">Start with Genesis 1 Quiz</a><a class=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors\" href=\"/bible-quizzes\">Browse All Quizzes</a></div></div></section><section class=\"py-16 bg-white\"><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 class=\"text-3xl font-bold text-center text-gray-900 mb-12\">Why Choose Our Bible Quizzes?</h2><div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\"><div class=\"text-center p-6\"><div class=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\"><svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"text-blue-600\"><path d=\"M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM7 7H17V9H7V7ZM7 11H17V13H7V11ZM7 15H13V17H7V15Z\" fill=\"currentColor\"></path></svg></div><h3 class=\"text-xl font-semibold text-gray-900 mb-3\">Comprehensive Coverage</h3><p class=\"text-gray-600\">Quizzes for all 66 Bible books with 16-25 questions each, covering key themes, characters, and events.</p></div><div class=\"text-center p-6\"><div class=\"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\"><svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"text-green-600\"><path d=\"M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z\" fill=\"currentColor\"></path></svg></div><h3 class=\"text-xl font-semibold text-gray-900 mb-3\">Detailed Explanations</h3><p class=\"text-gray-600\">Every answer includes biblical references and explanations to deepen your understanding.</p></div><div class=\"text-center p-6\"><div class=\"bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\"><svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"text-purple-600\"><path d=\"M4 2C3.45 2 3 2.45 3 3V21C3 21.55 3.45 22 4 22H20C20.55 22 21 21.55 21 21V3C21 2.45 20.55 2 20 2H4ZM6 4H18V20H6V4ZM8 6V8H16V6H8ZM8 10V12H16V10H8ZM8 14V16H13V14H8Z\" fill=\"currentColor\"></path></svg></div><h3 class=\"text-xl font-semibold text-gray-900 mb-3\">Scripture-Based</h3><p class=\"text-gray-600\">All questions are carefully crafted from biblical text with accurate theological content.</p></div></div></div></section><section class=\"py-16 bg-gray-50\"><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 class=\"text-3xl font-bold text-center text-gray-900 mb-12\">Featured Bible Quizzes</h2><div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\"><div class=\"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow\"><div class=\"p-6\"><h3 class=\"text-xl font-semibold text-gray-900 mb-3\">Genesis Chapter 1 Quiz</h3><p class=\"text-gray-600 mb-4\">Test your knowledge of the creation account</p><div class=\"flex items-center justify-between text-sm text-gray-500 mb-4\"><span class=\"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">Easy</span><span>18<!-- --> Questions</span><span>~<!-- -->8<!-- --> min</span></div><a class=\"block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\" href=\"/genesis-1-quiz\">Take Quiz</a></div></div><div class=\"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow\"><div class=\"p-6\"><h3 class=\"text-xl font-semibold text-gray-900 mb-3\">Complete Genesis Quiz</h3><p class=\"text-gray-600 mb-4\">Comprehensive quiz covering the entire book</p><div class=\"flex items-center justify-between text-sm text-gray-500 mb-4\"><span class=\"px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">Medium</span><span>25<!-- --> Questions</span><span>~<!-- -->12<!-- --> min</span></div><a class=\"block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\" href=\"/genesis-quiz\">Take Quiz</a></div></div><div class=\"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow\"><div class=\"p-6\"><h3 class=\"text-xl font-semibold text-gray-900 mb-3\">Matthew Quiz</h3><p class=\"text-gray-600 mb-4\">Test your knowledge of the Gospel of Matthew</p><div class=\"flex items-center justify-between text-sm text-gray-500 mb-4\"><span class=\"px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">Medium</span><span>22<!-- --> Questions</span><span>~<!-- -->10<!-- --> min</span></div><a class=\"block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\" href=\"/matthew-quiz\">Take Quiz</a></div></div></div></div></section><section class=\"py-16 bg-white\"><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><h2 class=\"text-3xl font-bold text-center text-gray-900 mb-12\">Explore Bible Books</h2><div class=\"grid grid-cols-1 md:grid-cols-2 gap-8\"><div><h3 class=\"text-2xl font-semibold text-gray-900 mb-6 flex items-center\"><span class=\"bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-medium mr-3\">Old Testament</span></h3><div class=\"grid grid-cols-2 gap-4\"><a class=\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group\" href=\"/genesis-chapters\"><h4 class=\"font-semibold text-gray-900 group-hover:text-blue-600\">Genesis</h4><p class=\"text-sm text-gray-600 mt-1\">50<!-- --> chapters</p></a><a class=\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group\" href=\"/exodus-quiz\"><h4 class=\"font-semibold text-gray-900 group-hover:text-blue-600\">Exodus</h4><p class=\"text-sm text-gray-600 mt-1\">40<!-- --> chapters</p></a></div></div><div><h3 class=\"text-2xl font-semibold text-gray-900 mb-6 flex items-center\"><span class=\"bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium mr-3\">New Testament</span></h3><div class=\"grid grid-cols-2 gap-4\"><a class=\"p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors group\" href=\"/matthew-quiz\"><h4 class=\"font-semibold text-gray-900 group-hover:text-green-600\">Matthew</h4><p class=\"text-sm text-gray-600 mt-1\">28<!-- --> chapters</p></a><a class=\"p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors group\" href=\"/john-quiz\"><h4 class=\"font-semibold text-gray-900 group-hover:text-green-600\">John</h4><p class=\"text-sm text-gray-600 mt-1\">21<!-- --> chapters</p></a></div></div></div><div class=\"text-center mt-8\"><a class=\"inline-flex items-center space-x-2 bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors\" href=\"/bible-quizzes\"><span>View All Bible Quizzes</span><svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"\"><path d=\"M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z\" fill=\"currentColor\"></path></svg></a></div></div></section><section class=\"py-16 bg-blue-600 text-white\"><div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\"><h2 class=\"text-3xl font-bold mb-4\">Ready to Test Your Bible Knowledge?</h2><p class=\"text-xl text-blue-100 mb-8\">Start with our most popular quiz and discover how well you know God&#x27;s Word.</p><a class=\"bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block\" href=\"/genesis-1-quiz\">Begin with Genesis 1 Quiz</a></div></section></div></main><footer class=\"bg-gray-900 text-white py-12\"><div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\"><div class=\"grid grid-cols-1 md:grid-cols-4 gap-8\"><div><h3 class=\"text-lg font-semibold mb-4\">Bible Quizzes</h3><ul class=\"space-y-2 text-gray-300\"><li><a href=\"/genesis-chapters\" class=\"hover:text-white\">Genesis Chapters</a></li><li><a href=\"/matthew-quiz\" class=\"hover:text-white\">Matthew Quiz</a></li><li><a href=\"/john-quiz\" class=\"hover:text-white\">John Quiz</a></li><li><a href=\"/bible-quizzes\" class=\"hover:text-white\">All Quizzes</a></li></ul></div><div><h3 class=\"text-lg font-semibold mb-4\">Study Resources</h3><ul class=\"space-y-2 text-gray-300\"><li><a href=\"/bible-study-guides\" class=\"hover:text-white\">Study Guides</a></li><li><a href=\"/bible-characters\" class=\"hover:text-white\">Character Studies</a></li><li><a href=\"/bible-timeline\" class=\"hover:text-white\">Bible Timeline</a></li></ul></div><div><h3 class=\"text-lg font-semibold mb-4\">Popular Topics</h3><ul class=\"space-y-2 text-gray-300\"><li><a href=\"/miracles-quiz\" class=\"hover:text-white\">Miracles Quiz</a></li><li><a href=\"/parables-quiz\" class=\"hover:text-white\">Parables Quiz</a></li><li><a href=\"/ten-commandments-quiz\" class=\"hover:text-white\">Ten Commandments</a></li></ul></div><div><h3 class=\"text-lg font-semibold mb-4\">Site Info</h3><ul class=\"space-y-2 text-gray-300\"><li><a href=\"/about\" class=\"hover:text-white\">About Us</a></li><li><a href=\"/contact\" class=\"hover:text-white\">Contact</a></li><li><a href=\"/privacy-policy\" class=\"hover:text-white\">Privacy Policy</a></li></ul></div></div><div class=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\"><p>© 2025 BibleQuiz. All rights reserved.</p></div></div></footer><div aria-live=\"polite\" aria-atomic=\"true\" class=\"sr-only\"></div><script src=\"/_next/static/chunks/webpack.js?v=1753935695760\" async=\"\"></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,\"1:HL[\\\"/_next/static/css/app/layout.css?v=1753935695760\\\",\\\"style\\\"]\\n0:D{\\\"name\\\":\\\"r2\\\",\\\"env\\\":\\\"Server\\\"}\\n\"])</script><script>self.__next_f.push([1,\"2:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n5:I[\\\"(app-pages-browser)/./app/components/icons/BibleIcon.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"BibleIcon\\\"]\\n6:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/link.js\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"\\\"]\\n7:I[\\\"(app-pages-browser)/./app/components/icons/BibleIcon.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"QuizIcon\\\"]\\n8:I[\\\"(app-pages-browser)/./app/components/icons/BibleIcon.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"CheckIcon\\\"]\\n9:I[\\\"(app-pages-browser)/./app/components/icons/BibleIcon.tsx\\\",[\\\"app/page\\\",\\\"static/chunks/app/page.js\\\"],\\\"ArrowRightIcon\\\"]\\nb:I[\\\"(app-pages-browser)/./app/components/seo/JsonLdSchema.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"default\\\"]\\nc:I[\\\"(app-pages-browser)/./app/components/AccessibilityProvider.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"AccessibilityProvider\\\"]\\nd:I[\\\"(app-pages-browser)/./app/components/Navigation.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"default\\\"]\\ne:I[\\\"(app-pages-browser)/./app/components/AccessibilityToolbar.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"default\\\"]\\nf:I[\\\"(app-pages-browser)/./app/components/PWAInstaller.tsx\\\",[\\\"app/layout\\\",\\\"static/chunks/app/layout.js\\\"],\\\"default\\\"]\\n10:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n11:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n15:I[\\\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\\\",[\\\"app-pages-internals\\\",\\\"static/chunks/app-pages-internals.js\\\"],\\\"\\\"]\\n3:D{\\\"name\\\":\\\"\\\",\\\"env\\\":\\\"Server\\\"}\\n4:D{\\\"name\\\":\\\"Home\\\",\\\"env\\\":\\\"Server\\\"}\\n\"])</script><script>self.__next_f.push([1,\"4:[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"min-h-screen\\\",\\\"children\\\":[[\\\"$\\\",\\\"section\\\",null,{\\\"className\\\":\\\"bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex justify-center mb-6\\\",\\\"children\\\":[\\\"$\\\",\\\"$L5\\\",null,{\\\"size\\\":80,\\\"className\\\":\\\"text-blue-200\\\"}]}],[\\\"$\\\",\\\"h1\\\",null,{\\\"className\\\":\\\"text-4xl md:text-6xl font-bold mb-6\\\",\\\"children\\\":\\\"Test Your Biblical Knowledge\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto\\\",\\\"children\\\":\\\"Interactive Bible quizzes covering all 66 books with detailed explanations and scripture references. Perfect for Bible study, Sunday school, and personal growth.\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex flex-col sm:flex-row gap-4 justify-center\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/genesis-1-quiz\\\",\\\"className\\\":\\\"bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1\\\",\\\"children\\\":\\\"Start with Genesis 1 Quiz\\\"}],[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/bible-quizzes\\\",\\\"className\\\":\\\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors\\\",\\\"children\\\":\\\"Browse All Quizzes\\\"}]]}]]}]}],[\\\"$\\\",\\\"section\\\",null,{\\\"className\\\":\\\"py-16 bg-white\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"h2\\\",null,{\\\"className\\\":\\\"text-3xl font-bold text-center text-gray-900 mb-12\\\",\\\"children\\\":\\\"Why Choose Our Bible Quizzes?\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"grid grid-cols-1 md:grid-cols-3 gap-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"text-center p-6\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\\\",\\\"children\\\":[\\\"$\\\",\\\"$L7\\\",null,{\\\"size\\\":32,\\\"className\\\":\\\"text-blue-600\\\"}]}],[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-xl font-semibold text-gray-900 mb-3\\\",\\\"children\\\":\\\"Comprehensive Coverage\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-gray-600\\\",\\\"children\\\":\\\"Quizzes for all 66 Bible books with 16-25 questions each, covering key themes, characters, and events.\\\"}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"text-center p-6\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\\\",\\\"children\\\":[\\\"$\\\",\\\"$L8\\\",null,{\\\"size\\\":32,\\\"className\\\":\\\"text-green-600\\\"}]}],[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-xl font-semibold text-gray-900 mb-3\\\",\\\"children\\\":\\\"Detailed Explanations\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-gray-600\\\",\\\"children\\\":\\\"Every answer includes biblical references and explanations to deepen your understanding.\\\"}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"text-center p-6\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\\\",\\\"children\\\":[\\\"$\\\",\\\"$L5\\\",null,{\\\"size\\\":32,\\\"className\\\":\\\"text-purple-600\\\"}]}],[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-xl font-semibold text-gray-900 mb-3\\\",\\\"children\\\":\\\"Scripture-Based\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-gray-600\\\",\\\"children\\\":\\\"All questions are carefully crafted from biblical text with accurate theological content.\\\"}]]}]]}]]}]}],[\\\"$\\\",\\\"section\\\",null,{\\\"className\\\":\\\"py-16 bg-gray-50\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"h2\\\",null,{\\\"className\\\":\\\"text-3xl font-bold text-center text-gray-900 mb-12\\\",\\\"children\\\":\\\"Featured Bible Quizzes\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"grid grid-cols-1 md:grid-cols-3 gap-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",\\\"0\\\",{\\\"className\\\":\\\"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"p-6\\\",\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-xl font-semibold text-gray-900 mb-3\\\",\\\"children\\\":\\\"Genesis Chapter 1 Quiz\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-gray-600 mb-4\\\",\\\"children\\\":\\\"Test your knowledge of the creation account\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex items-center justify-between text-sm text-gray-500 mb-4\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\\\",\\\"children\\\":\\\"Easy\\\"}],[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":[18,\\\" Questions\\\"]}],[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":[\\\"~\\\",8,\\\" min\\\"]}]]}],[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/genesis-1-quiz\\\",\\\"className\\\":\\\"block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\\\",\\\"children\\\":\\\"Take Quiz\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"1\\\",{\\\"className\\\":\\\"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"p-6\\\",\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-xl font-semibold text-gray-900 mb-3\\\",\\\"children\\\":\\\"Complete Genesis Quiz\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-gray-600 mb-4\\\",\\\"children\\\":\\\"Comprehensive quiz covering the entire book\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex items-center justify-between text-sm text-gray-500 mb-4\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\\\",\\\"children\\\":\\\"Medium\\\"}],[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":[25,\\\" Questions\\\"]}],[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":[\\\"~\\\",12,\\\" min\\\"]}]]}],[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/genesis-quiz\\\",\\\"className\\\":\\\"block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\\\",\\\"children\\\":\\\"Take Quiz\\\"}]]}]}],[\\\"$\\\",\\\"div\\\",\\\"2\\\",{\\\"className\\\":\\\"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"p-6\\\",\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-xl font-semibold text-gray-900 mb-3\\\",\\\"children\\\":\\\"Matthew Quiz\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-gray-600 mb-4\\\",\\\"children\\\":\\\"Test your knowledge of the Gospel of Matthew\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"flex items-center justify-between text-sm text-gray-500 mb-4\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\\\",\\\"children\\\":\\\"Medium\\\"}],[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":[22,\\\" Questions\\\"]}],[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":[\\\"~\\\",10,\\\" min\\\"]}]]}],[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/matthew-quiz\\\",\\\"className\\\":\\\"block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\\\",\\\"children\\\":\\\"Take Quiz\\\"}]]}]}]]}]]}]}],[\\\"$\\\",\\\"section\\\",null,{\\\"className\\\":\\\"py-16 bg-white\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"h2\\\",null,{\\\"className\\\":\\\"text-3xl font-bold text-center text-gray-900 mb-12\\\",\\\"children\\\":\\\"Explore Bible Books\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"grid grid-cols-1 md:grid-cols-2 gap-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-2xl font-semibold text-gray-900 mb-6 flex items-center\\\",\\\"children\\\":[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-medium mr-3\\\",\\\"children\\\":\\\"Old Testament\\\"}]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"grid grid-cols-2 gap-4\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L6\\\",\\\"genesis\\\",{\\\"href\\\":\\\"/genesis-chapters\\\",\\\"className\\\":\\\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"font-semibold text-gray-900 group-hover:text-blue-600\\\",\\\"children\\\":\\\"Genesis\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-sm text-gray-600 mt-1\\\",\\\"children\\\":[50,\\\" chapters\\\"]}]]}],[\\\"$\\\",\\\"$L6\\\",\\\"exodus\\\",{\\\"href\\\":\\\"/exodus-quiz\\\",\\\"className\\\":\\\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"font-semibold text-gray-900 group-hover:text-blue-600\\\",\\\"children\\\":\\\"Exodus\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-sm text-gray-600 mt-1\\\",\\\"children\\\":[40,\\\" chapters\\\"]}]]}]]}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-2xl font-semibold text-gray-900 mb-6 flex items-center\\\",\\\"children\\\":[\\\"$\\\",\\\"span\\\",null,{\\\"className\\\":\\\"bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium mr-3\\\",\\\"children\\\":\\\"New Testament\\\"}]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"grid grid-cols-2 gap-4\\\",\\\"children\\\":[[\\\"$\\\",\\\"$L6\\\",\\\"matthew\\\",{\\\"href\\\":\\\"/matthew-quiz\\\",\\\"className\\\":\\\"p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors group\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"font-semibold text-gray-900 group-hover:text-green-600\\\",\\\"children\\\":\\\"Matthew\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-sm text-gray-600 mt-1\\\",\\\"children\\\":[28,\\\" chapters\\\"]}]]}],[\\\"$\\\",\\\"$L6\\\",\\\"john\\\",{\\\"href\\\":\\\"/john-quiz\\\",\\\"className\\\":\\\"p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors group\\\",\\\"children\\\":[[\\\"$\\\",\\\"h4\\\",null,{\\\"className\\\":\\\"font-semibold text-gray-900 group-hover:text-green-600\\\",\\\"children\\\":\\\"John\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-sm text-gray-600 mt-1\\\",\\\"children\\\":[21,\\\" chapters\\\"]}]]}]]}]]}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"text-center mt-8\\\",\\\"children\\\":[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/bible-quizzes\\\",\\\"className\\\":\\\"inline-flex items-center space-x-2 bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors\\\",\\\"children\\\":[[\\\"$\\\",\\\"span\\\",null,{\\\"children\\\":\\\"View All Bible Quizzes\\\"}],[\\\"$\\\",\\\"$L9\\\",null,{\\\"size\\\":20}]]}]}]]}]}],[\\\"$\\\",\\\"section\\\",null,{\\\"className\\\":\\\"py-16 bg-blue-600 text-white\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\\\",\\\"children\\\":[[\\\"$\\\",\\\"h2\\\",null,{\\\"className\\\":\\\"text-3xl font-bold mb-4\\\",\\\"children\\\":\\\"Ready to Test Your Bible Knowledge?\\\"}],[\\\"$\\\",\\\"p\\\",null,{\\\"className\\\":\\\"text-xl text-blue-100 mb-8\\\",\\\"children\\\":\\\"Start with our most popular quiz and discover how well you know God's Word.\\\"}],[\\\"$\\\",\\\"$L6\\\",null,{\\\"href\\\":\\\"/genesis-1-quiz\\\",\\\"className\\\":\\\"bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block\\\",\\\"children\\\":\\\"Begin with Genesis 1 Quiz\\\"}]]}]}]]}]\\n\"])</script><script>self.__next_f.push([1,\"a:D{\\\"name\\\":\\\"RootLayout\\\",\\\"env\\\":\\\"Server\\\"}\\n12:D{\\\"name\\\":\\\"NotFound\\\",\\\"env\\\":\\\"Server\\\"}\\n12:[[\\\"$\\\",\\\"title\\\",null,{\\\"children\\\":\\\"404: This page could not be found.\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"style\\\":{\\\"fontFamily\\\":\\\"system-ui,\\\\\\\"Segoe UI\\\\\\\",Roboto,Helvetica,Arial,sans-serif,\\\\\\\"Apple Color Emoji\\\\\\\",\\\\\\\"Segoe UI Emoji\\\\\\\"\\\",\\\"height\\\":\\\"100vh\\\",\\\"textAlign\\\":\\\"center\\\",\\\"display\\\":\\\"flex\\\",\\\"flexDirection\\\":\\\"column\\\",\\\"alignItems\\\":\\\"center\\\",\\\"justifyContent\\\":\\\"center\\\"},\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"style\\\",null,{\\\"dangerouslySetInnerHTML\\\":{\\\"__html\\\":\\\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\\\"}}],[\\\"$\\\",\\\"h1\\\",null,{\\\"className\\\":\\\"next-error-h1\\\",\\\"style\\\":{\\\"display\\\":\\\"inline-block\\\",\\\"margin\\\":\\\"0 20px 0 0\\\",\\\"padding\\\":\\\"0 23px 0 0\\\",\\\"fontSize\\\":24,\\\"fontWeight\\\":500,\\\"verticalAlign\\\":\\\"top\\\",\\\"lineHeight\\\":\\\"49px\\\"},\\\"children\\\":\\\"404\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"style\\\":{\\\"display\\\":\\\"inline-block\\\"},\\\"children\\\":[\\\"$\\\",\\\"h2\\\",null,{\\\"style\\\":{\\\"fontSize\\\":14,\\\"fontWeight\\\":400,\\\"lineHeight\\\":\\\"49px\\\",\\\"margin\\\":0},\\\"children\\\":\\\"This page could not be found.\\\"}]}]]}]}]]\\n\"])</script><script>self.__next_f.push([1,\"a:[\\\"$\\\",\\\"html\\\",null,{\\\"lang\\\":\\\"en\\\",\\\"children\\\":[[\\\"$\\\",\\\"head\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"manifest\\\",\\\"href\\\":\\\"/manifest.json\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"theme-color\\\",\\\"content\\\":\\\"#2563eb\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"mobile-web-app-capable\\\",\\\"content\\\":\\\"yes\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-mobile-web-app-capable\\\",\\\"content\\\":\\\"yes\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-mobile-web-app-status-bar-style\\\",\\\"content\\\":\\\"default\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-mobile-web-app-title\\\",\\\"content\\\":\\\"Bible Quiz\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"href\\\":\\\"/icon-192x192.png\\\"}],[\\\"$\\\",\\\"$Lb\\\",null,{\\\"type\\\":\\\"website\\\"}]]}],[\\\"$\\\",\\\"body\\\",null,{\\\"className\\\":\\\"__variable_1e4310 __variable_c3aa02 antialiased bg-gray-50\\\",\\\"children\\\":[\\\"$\\\",\\\"$Lc\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"#main-content\\\",\\\"className\\\":\\\"skip-link\\\",\\\"children\\\":\\\"Skip to main content\\\"}],[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"#navigation\\\",\\\"className\\\":\\\"skip-link\\\",\\\"children\\\":\\\"Skip to navigation\\\"}],[\\\"$\\\",\\\"$Ld\\\",null,{}],[\\\"$\\\",\\\"$Le\\\",null,{}],[\\\"$\\\",\\\"$Lf\\\",null,{}],[\\\"$\\\",\\\"main\\\",null,{\\\"id\\\":\\\"main-content\\\",\\\"className\\\":\\\"min-h-screen\\\",\\\"role\\\":\\\"main\\\",\\\"children\\\":[\\\"$\\\",\\\"$L10\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"segmentPath\\\":[\\\"children\\\"],\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L11\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$12\\\",\\\"notFoundStyles\\\":[]}]}],[\\\"$\\\",\\\"footer\\\",null,{\\\"className\\\":\\\"bg-gray-900 text-white py-12\\\",\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"grid grid-cols-1 md:grid-cols-4 gap-8\\\",\\\"children\\\":[[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-lg font-semibold mb-4\\\",\\\"children\\\":\\\"Bible Quizzes\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"space-y-2 text-gray-300\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/genesis-chapters\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Genesis Chapters\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/matthew-quiz\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Matthew Quiz\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/john-quiz\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"John Quiz\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/bible-quizzes\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"All Quizzes\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-lg font-semibold mb-4\\\",\\\"children\\\":\\\"Study Resources\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"space-y-2 text-gray-300\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/bible-study-guides\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Study Guides\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/bible-characters\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Character Studies\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/bible-timeline\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Bible Timeline\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-lg font-semibold mb-4\\\",\\\"children\\\":\\\"Popular Topics\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"space-y-2 text-gray-300\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/miracles-quiz\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Miracles Quiz\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/parables-quiz\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Parables Quiz\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/ten-commandments-quiz\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Ten Commandments\\\"}]}]]}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"h3\\\",null,{\\\"className\\\":\\\"text-lg font-semibold mb-4\\\",\\\"children\\\":\\\"Site Info\\\"}],[\\\"$\\\",\\\"ul\\\",null,{\\\"className\\\":\\\"space-y-2 text-gray-300\\\",\\\"children\\\":[[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/about\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"About Us\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/contact\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Contact\\\"}]}],[\\\"$\\\",\\\"li\\\",null,{\\\"children\\\":[\\\"$\\\",\\\"a\\\",null,{\\\"href\\\":\\\"/privacy-policy\\\",\\\"className\\\":\\\"hover:text-white\\\",\\\"children\\\":\\\"Privacy Policy\\\"}]}]]}]]}]]}],[\\\"$\\\",\\\"div\\\",null,{\\\"className\\\":\\\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\\\",\\\"children\\\":[\\\"$\\\",\\\"p\\\",null,{\\\"children\\\":\\\"© 2025 BibleQuiz. All rights reserved.\\\"}]}]]}]}]]}]}]]}]\\n\"])</script><script>self.__next_f.push([1,\"13:D{\\\"name\\\":\\\"rQ\\\",\\\"env\\\":\\\"Server\\\"}\\n13:null\\n14:D{\\\"name\\\":\\\"\\\",\\\"env\\\":\\\"Server\\\"}\\n16:[]\\n0:[\\\"$\\\",\\\"$L2\\\",null,{\\\"buildId\\\":\\\"development\\\",\\\"assetPrefix\\\":\\\"\\\",\\\"urlParts\\\":[\\\"\\\",\\\"\\\"],\\\"initialTree\\\":[\\\"\\\",{\\\"children\\\":[\\\"__PAGE__\\\",{}]},\\\"$undefined\\\",\\\"$undefined\\\",true],\\\"initialSeedData\\\":[\\\"\\\",{\\\"children\\\":[\\\"__PAGE__\\\",{},[[\\\"$L3\\\",\\\"$4\\\",null],null],null]},[[[[\\\"$\\\",\\\"link\\\",\\\"0\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/app/layout.css?v=1753935695760\\\",\\\"precedence\\\":\\\"next_static/css/app/layout.css\\\",\\\"crossOrigin\\\":\\\"$undefined\\\"}]],\\\"$a\\\"],null],null],\\\"couldBeIntercepted\\\":false,\\\"initialHead\\\":[\\\"$13\\\",\\\"$L14\\\"],\\\"globalErrorComponent\\\":\\\"$15\\\",\\\"missingSlots\\\":\\\"$W16\\\"}]\\n\"])</script><script>self.__next_f.push([1,\"14:[[\\\"$\\\",\\\"meta\\\",\\\"0\\\",{\\\"name\\\":\\\"viewport\\\",\\\"content\\\":\\\"width=device-width, initial-scale=1\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"charSet\\\":\\\"utf-8\\\"}],[\\\"$\\\",\\\"title\\\",\\\"2\\\",{\\\"children\\\":\\\"Bible Quiz - Test Your Biblical Knowledge | BibleQuiz\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"3\\\",{\\\"name\\\":\\\"description\\\",\\\"content\\\":\\\"Test your Bible knowledge with comprehensive quizzes covering all 66 books. Interactive questions with detailed explanations and scripture references. Perfect for Bible study!\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"4\\\",{\\\"name\\\":\\\"keywords\\\",\\\"content\\\":\\\"bible quiz,scripture test,biblical knowledge,bible study,christian quiz\\\"}],[\\\"$\\\",\\\"link\\\",\\\"5\\\",{\\\"rel\\\":\\\"icon\\\",\\\"href\\\":\\\"/favicon.ico\\\",\\\"type\\\":\\\"image/x-icon\\\",\\\"sizes\\\":\\\"16x16\\\"}]]\\n3:null\\n\"])</script></body></html>", "httpHeaders": "HTTP/1.1 200 OK\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: DENY\r\nX-XSS-Protection: 1; mode=block\r\nReferrer-Policy: origin-when-cross-origin\r\nVary: RSC, Next-Router-State-Tree, Next-Router-Prefetch, Accept-Encoding\r\nCache-Control: no-store, must-revalidate\r\nContent-Type: text/html; charset=utf-8\r\nDate: Thu, 31 Jul 2025 04:21:35 GMT\r\nConnection: keep-alive\r\nKeep-Alive: timeout=5\r\n\r\n", "tests": {"hasTitle": true, "hasNextJs": true, "hasReact": false, "hasQuizContent": true, "hasBibleContent": true, "pageTitle": "Bible Quiz - Test Your Biblical Knowledge | BibleQuiz", "quizLinks": ["/bible-quizzes", "/old-testament-quizzes", "/new-testament-quizzes", "/genesis-chapters", "/matthew-quiz", "/bible-characters", "/genesis-1-quiz", "/bible-quizzes", "/genesis-1-quiz", "/genesis-quiz", "/matthew-quiz", "/genesis-chapters", "/exodus-quiz", "/matthew-quiz", "/john-quiz", "/bible-quizzes", "/genesis-1-quiz", "/genesis-chapters", "/matthew-quiz", "/john-quiz", "/bible-quizzes", "/bible-study-guides", "/bible-characters", "/bible-timeline", "/miracles-quiz", "/parables-quiz", "/ten-commandments-quiz"], "quizPage1": {"hasQuizElements": true, "hasInteractiveElements": true, "hasTimer": false, "hasProgress": false, "contentLength": 46358}, "bypassPage1": {"contentLength": 46416, "different": true}, "quizPage2": {"hasQuizElements": true, "hasInteractiveElements": true, "hasTimer": false, "hasProgress": false, "contentLength": 80656}, "bypassPage2": {"contentLength": 80696, "different": true}, "quizPage3": {"hasQuizElements": true, "hasInteractiveElements": true, "hasTimer": false, "hasProgress": false, "contentLength": 80656}, "bypassPage3": {"contentLength": 80696, "different": true}, "apiapihealth": {"accessible": true, "response": "HTTP/1.1 200 OK\r"}, "apiapiquiz": {"accessible": true, "response": "HTTP/1.1 404 Not Found\r"}, "apiapiquestions": {"accessible": true, "response": "HTTP/1.1 404 Not Found\r"}}}