import { BibleBook } from '@/lib/types';

// Comprehensive data for all 66 books of the Bible
export const BIBLE_BOOKS: BibleBook[] = [
  // OLD TESTAMENT (39 books)
  {
    id: 'genesis',
    name: '<PERSON>',
    slug: 'genesis',
    testament: 'old',
    chapters: 50,
    description: 'The book of beginnings - creation, fall, flood, and the patriarchs',
    keyThemes: ['Creation', 'Fall', 'Covenant', 'Patriarchs', 'Providence'],
    difficulty: 'beginner',
    estimatedTime: 8
  },
  {
    id: 'exodus',
    name: 'Exodus',
    slug: 'exodus',
    testament: 'old',
    chapters: 40,
    description: 'Israel\'s deliverance from Egypt and the giving of the Law',
    keyThemes: ['Deliverance', 'Law', 'Covenant', 'Tabernacle', 'Worship'],
    difficulty: 'beginner',
    estimatedTime: 7
  },
  {
    id: 'leviticus',
    name: '<PERSON><PERSON>',
    slug: 'leviticus',
    testament: 'old',
    chapters: 27,
    description: 'Laws for worship, sacrifice, and holy living',
    keyThemes: ['Holiness', 'Sacrifice', 'Priesthood', 'Purity', 'Atonement'],
    difficulty: 'intermediate',
    estimatedTime: 6
  },
  {
    id: 'numbers',
    name: 'Numbers',
    slug: 'numbers',
    testament: 'old',
    chapters: 36,
    description: 'Israel\'s wilderness wandering and preparation for the Promised Land',
    keyThemes: ['Wandering', 'Faithfulness', 'Judgment', 'Census', 'Rebellion'],
    difficulty: 'intermediate',
    estimatedTime: 7
  },
  {
    id: 'deuteronomy',
    name: 'Deuteronomy',
    slug: 'deuteronomy',
    testament: 'old',
    chapters: 34,
    description: 'Moses\' final speeches and the renewal of the covenant',
    keyThemes: ['Covenant', 'Obedience', 'Love', 'Blessing', 'Law'],
    difficulty: 'intermediate',
    estimatedTime: 7
  },
  {
    id: 'joshua',
    name: 'Joshua',
    slug: 'joshua',
    testament: 'old',
    chapters: 24,
    description: 'The conquest and settlement of the Promised Land',
    keyThemes: ['Conquest', 'Faith', 'Obedience', 'Inheritance', 'Leadership'],
    difficulty: 'beginner',
    estimatedTime: 6
  },
  {
    id: 'judges',
    name: 'Judges',
    slug: 'judges',
    testament: 'old',
    chapters: 21,
    description: 'The cycle of sin, oppression, and deliverance in Israel',
    keyThemes: ['Apostasy', 'Deliverance', 'Leadership', 'Cycle', 'Judges'],
    difficulty: 'intermediate',
    estimatedTime: 6
  },
  {
    id: 'ruth',
    name: 'Ruth',
    slug: 'ruth',
    testament: 'old',
    chapters: 4,
    description: 'A beautiful story of loyalty, love, and redemption',
    keyThemes: ['Loyalty', 'Redemption', 'Love', 'Providence', 'Kinsman-redeemer'],
    difficulty: 'beginner',
    estimatedTime: 4
  },
  {
    id: '1-samuel',
    name: '1 Samuel',
    slug: '1-samuel',
    testament: 'old',
    chapters: 31,
    description: 'Samuel, Saul, and the rise of David',
    keyThemes: ['Leadership', 'Kingship', 'Obedience', 'Heart', 'Anointing'],
    difficulty: 'beginner',
    estimatedTime: 7
  },
  {
    id: '2-samuel',
    name: '2 Samuel',
    slug: '2-samuel',
    testament: 'old',
    chapters: 24,
    description: 'David\'s reign as king over Israel',
    keyThemes: ['Kingship', 'Covenant', 'Sin', 'Forgiveness', 'Kingdom'],
    difficulty: 'beginner',
    estimatedTime: 6
  },
  {
    id: '1-kings',
    name: '1 Kings',
    slug: '1-kings',
    testament: 'old',
    chapters: 22,
    description: 'Solomon\'s reign and the division of the kingdom',
    keyThemes: ['Wisdom', 'Temple', 'Division', 'Idolatry', 'Prophets'],
    difficulty: 'intermediate',
    estimatedTime: 6
  },
  {
    id: '2-kings',
    name: '2 Kings',
    slug: '2-kings',
    testament: 'old',
    chapters: 25,
    description: 'The decline and fall of Israel and Judah',
    keyThemes: ['Decline', 'Exile', 'Prophets', 'Judgment', 'Unfaithfulness'],
    difficulty: 'intermediate',
    estimatedTime: 6
  },
  {
    id: '1-chronicles',
    name: '1 Chronicles',
    slug: '1-chronicles',
    testament: 'old',
    chapters: 29,
    description: 'Genealogies and David\'s reign from a priestly perspective',
    keyThemes: ['Genealogy', 'Temple', 'Worship', 'Priestly', 'David'],
    difficulty: 'advanced',
    estimatedTime: 6
  },
  {
    id: '2-chronicles',
    name: '2 Chronicles',
    slug: '2-chronicles',
    testament: 'old',
    chapters: 36,
    description: 'The history of Judah with emphasis on temple worship',
    keyThemes: ['Temple', 'Worship', 'Reform', 'Faithfulness', 'Restoration'],
    difficulty: 'advanced',
    estimatedTime: 7
  },
  {
    id: 'ezra',
    name: 'Ezra',
    slug: 'ezra',
    testament: 'old',
    chapters: 10,
    description: 'The return from exile and rebuilding of the temple',
    keyThemes: ['Return', 'Restoration', 'Temple', 'Law', 'Separation'],
    difficulty: 'intermediate',
    estimatedTime: 5
  },
  {
    id: 'nehemiah',
    name: 'Nehemiah',
    slug: 'nehemiah',
    testament: 'old',
    chapters: 13,
    description: 'Rebuilding Jerusalem\'s walls and spiritual renewal',
    keyThemes: ['Rebuilding', 'Leadership', 'Prayer', 'Reform', 'Perseverance'],
    difficulty: 'intermediate',
    estimatedTime: 5
  },
  {
    id: 'esther',
    name: 'Esther',
    slug: 'esther',
    testament: 'old',
    chapters: 10,
    description: 'God\'s providence in preserving His people',
    keyThemes: ['Providence', 'Courage', 'Deliverance', 'Identity', 'Faithfulness'],
    difficulty: 'beginner',
    estimatedTime: 5
  },
  {
    id: 'job',
    name: 'Job',
    slug: 'job',
    testament: 'old',
    chapters: 42,
    description: 'The problem of suffering and God\'s sovereignty',
    keyThemes: ['Suffering', 'Faith', 'Sovereignty', 'Wisdom', 'Justice'],
    difficulty: 'advanced',
    estimatedTime: 8
  },
  {
    id: 'psalms',
    name: 'Psalms',
    slug: 'psalms',
    testament: 'old',
    chapters: 150,
    description: 'Songs of worship, praise, and prayer',
    keyThemes: ['Worship', 'Praise', 'Prayer', 'Lament', 'Trust'],
    difficulty: 'beginner',
    estimatedTime: 12
  },
  {
    id: 'proverbs',
    name: 'Proverbs',
    slug: 'proverbs',
    testament: 'old',
    chapters: 31,
    description: 'Practical wisdom for daily living',
    keyThemes: ['Wisdom', 'Righteousness', 'Folly', 'Character', 'Fear of the Lord'],
    difficulty: 'beginner',
    estimatedTime: 7
  },
  {
    id: 'ecclesiastes',
    name: 'Ecclesiastes',
    slug: 'ecclesiastes',
    testament: 'old',
    chapters: 12,
    description: 'The meaning of life and the vanity of worldly pursuits',
    keyThemes: ['Vanity', 'Meaning', 'Wisdom', 'Time', 'Fear of God'],
    difficulty: 'advanced',
    estimatedTime: 5
  },
  {
    id: 'song-of-solomon',
    name: 'Song of Solomon',
    slug: 'song-of-solomon',
    testament: 'old',
    chapters: 8,
    description: 'A celebration of love and marriage',
    keyThemes: ['Love', 'Marriage', 'Beauty', 'Intimacy', 'Devotion'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'isaiah',
    name: 'Isaiah',
    slug: 'isaiah',
    testament: 'old',
    chapters: 66,
    description: 'Prophecies of judgment and salvation, including Messianic prophecies',
    keyThemes: ['Holiness', 'Salvation', 'Messiah', 'Judgment', 'Comfort'],
    difficulty: 'advanced',
    estimatedTime: 10
  },
  {
    id: 'jeremiah',
    name: 'Jeremiah',
    slug: 'jeremiah',
    testament: 'old',
    chapters: 52,
    description: 'The weeping prophet\'s call to repentance',
    keyThemes: ['Judgment', 'Repentance', 'New Covenant', 'Faithfulness', 'Hope'],
    difficulty: 'advanced',
    estimatedTime: 9
  },
  {
    id: 'lamentations',
    name: 'Lamentations',
    slug: 'lamentations',
    testament: 'old',
    chapters: 5,
    description: 'Mourning over Jerusalem\'s destruction',
    keyThemes: ['Lament', 'Grief', 'Hope', 'Faithfulness', 'Restoration'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'ezekiel',
    name: 'Ezekiel',
    slug: 'ezekiel',
    testament: 'old',
    chapters: 48,
    description: 'Visions of God\'s glory and the restoration of Israel',
    keyThemes: ['Glory', 'Vision', 'Restoration', 'Responsibility', 'New Heart'],
    difficulty: 'advanced',
    estimatedTime: 9
  },
  {
    id: 'daniel',
    name: 'Daniel',
    slug: 'daniel',
    testament: 'old',
    chapters: 12,
    description: 'Faithfulness in exile and apocalyptic visions',
    keyThemes: ['Faithfulness', 'Sovereignty', 'Prophecy', 'Kingdom', 'Persecution'],
    difficulty: 'intermediate',
    estimatedTime: 5
  },
  {
    id: 'hosea',
    name: 'Hosea',
    slug: 'hosea',
    testament: 'old',
    chapters: 14,
    description: 'God\'s unfailing love despite Israel\'s unfaithfulness',
    keyThemes: ['Love', 'Faithfulness', 'Adultery', 'Restoration', 'Covenant'],
    difficulty: 'intermediate',
    estimatedTime: 5
  },
  {
    id: 'joel',
    name: 'Joel',
    slug: 'joel',
    testament: 'old',
    chapters: 3,
    description: 'The Day of the Lord and the outpouring of the Spirit',
    keyThemes: ['Day of the Lord', 'Repentance', 'Spirit', 'Judgment', 'Restoration'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: 'amos',
    name: 'Amos',
    slug: 'amos',
    testament: 'old',
    chapters: 9,
    description: 'A call for justice and righteousness',
    keyThemes: ['Justice', 'Righteousness', 'Judgment', 'Social Reform', 'Restoration'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'obadiah',
    name: 'Obadiah',
    slug: 'obadiah',
    testament: 'old',
    chapters: 1,
    description: 'Judgment against Edom and hope for Israel',
    keyThemes: ['Judgment', 'Pride', 'Justice', 'Restoration', 'Kingdom'],
    difficulty: 'intermediate',
    estimatedTime: 2
  },
  {
    id: 'jonah',
    name: 'Jonah',
    slug: 'jonah',
    testament: 'old',
    chapters: 4,
    description: 'God\'s mercy extends to all nations',
    keyThemes: ['Mercy', 'Repentance', 'Missions', 'Obedience', 'Compassion'],
    difficulty: 'beginner',
    estimatedTime: 4
  },
  {
    id: 'micah',
    name: 'Micah',
    slug: 'micah',
    testament: 'old',
    chapters: 7,
    description: 'Justice, mercy, and walking humbly with God',
    keyThemes: ['Justice', 'Mercy', 'Humility', 'Messiah', 'Restoration'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'nahum',
    name: 'Nahum',
    slug: 'nahum',
    testament: 'old',
    chapters: 3,
    description: 'The fall of Nineveh and God\'s justice',
    keyThemes: ['Justice', 'Judgment', 'Comfort', 'Sovereignty', 'Vengeance'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: 'habakkuk',
    name: 'Habakkuk',
    slug: 'habakkuk',
    testament: 'old',
    chapters: 3,
    description: 'Wrestling with God\'s justice and learning to live by faith',
    keyThemes: ['Faith', 'Justice', 'Trust', 'Prayer', 'Sovereignty'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: 'zephaniah',
    name: 'Zephaniah',
    slug: 'zephaniah',
    testament: 'old',
    chapters: 3,
    description: 'The Day of the Lord and the promise of restoration',
    keyThemes: ['Day of the Lord', 'Judgment', 'Restoration', 'Rejoicing', 'Remnant'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: 'haggai',
    name: 'Haggai',
    slug: 'haggai',
    testament: 'old',
    chapters: 2,
    description: 'Rebuilding the temple and putting God first',
    keyThemes: ['Priorities', 'Temple', 'Blessing', 'Glory', 'Obedience'],
    difficulty: 'intermediate',
    estimatedTime: 2
  },
  {
    id: 'zechariah',
    name: 'Zechariah',
    slug: 'zechariah',
    testament: 'old',
    chapters: 14,
    description: 'Visions of restoration and the coming Messiah',
    keyThemes: ['Restoration', 'Messiah', 'Vision', 'Cleansing', 'Kingdom'],
    difficulty: 'advanced',
    estimatedTime: 5
  },
  {
    id: 'malachi',
    name: 'Malachi',
    slug: 'malachi',
    testament: 'old',
    chapters: 4,
    description: 'The final prophet\'s call to faithfulness',
    keyThemes: ['Faithfulness', 'Worship', 'Tithing', 'Marriage', 'Messenger'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },

  // NEW TESTAMENT (27 books)
  {
    id: 'matthew',
    name: 'Matthew',
    slug: 'matthew',
    testament: 'new',
    chapters: 28,
    description: 'Jesus as the promised Messiah and King',
    keyThemes: ['Messiah', 'Kingdom', 'Fulfillment', 'Teaching', 'Disciples'],
    difficulty: 'beginner',
    estimatedTime: 7
  },
  {
    id: 'mark',
    name: 'Mark',
    slug: 'mark',
    testament: 'new',
    chapters: 16,
    description: 'Jesus as the suffering Servant',
    keyThemes: ['Servant', 'Action', 'Suffering', 'Cross', 'Discipleship'],
    difficulty: 'beginner',
    estimatedTime: 5
  },
  {
    id: 'luke',
    name: 'Luke',
    slug: 'luke',
    testament: 'new',
    chapters: 24,
    description: 'Jesus as the perfect man and Savior of all',
    keyThemes: ['Salvation', 'Compassion', 'Prayer', 'Holy Spirit', 'Joy'],
    difficulty: 'beginner',
    estimatedTime: 6
  },
  {
    id: 'john',
    name: 'John',
    slug: 'john',
    testament: 'new',
    chapters: 21,
    description: 'Jesus as the Son of God and the Word made flesh',
    keyThemes: ['Deity', 'Eternal Life', 'Love', 'Light', 'Belief'],
    difficulty: 'intermediate',
    estimatedTime: 6
  },
  {
    id: 'acts',
    name: 'Acts',
    slug: 'acts',
    testament: 'new',
    chapters: 28,
    description: 'The early church and the spread of the gospel',
    keyThemes: ['Holy Spirit', 'Church', 'Mission', 'Persecution', 'Growth'],
    difficulty: 'intermediate',
    estimatedTime: 7
  },
  {
    id: 'romans',
    name: 'Romans',
    slug: 'romans',
    testament: 'new',
    chapters: 16,
    description: 'The gospel of God\'s righteousness through faith',
    keyThemes: ['Salvation', 'Righteousness', 'Faith', 'Grace', 'Sanctification'],
    difficulty: 'advanced',
    estimatedTime: 6
  },
  {
    id: '1-corinthians',
    name: '1 Corinthians',
    slug: '1-corinthians',
    testament: 'new',
    chapters: 16,
    description: 'Addressing problems in the Corinthian church',
    keyThemes: ['Unity', 'Wisdom', 'Morality', 'Gifts', 'Love', 'Resurrection'],
    difficulty: 'intermediate',
    estimatedTime: 6
  },
  {
    id: '2-corinthians',
    name: '2 Corinthians',
    slug: '2-corinthians',
    testament: 'new',
    chapters: 13,
    description: 'Paul\'s defense of his ministry and apostleship',
    keyThemes: ['Ministry', 'Suffering', 'Comfort', 'Giving', 'Weakness'],
    difficulty: 'intermediate',
    estimatedTime: 5
  },
  {
    id: 'galatians',
    name: 'Galatians',
    slug: 'galatians',
    testament: 'new',
    chapters: 6,
    description: 'Freedom in Christ versus legalism',
    keyThemes: ['Freedom', 'Faith', 'Law', 'Spirit', 'Justification'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'ephesians',
    name: 'Ephesians',
    slug: 'ephesians',
    testament: 'new',
    chapters: 6,
    description: 'Our identity and calling in Christ',
    keyThemes: ['Identity', 'Unity', 'Spiritual Warfare', 'Grace', 'Church'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'philippians',
    name: 'Philippians',
    slug: 'philippians',
    testament: 'new',
    chapters: 4,
    description: 'Joy and contentment in Christ',
    keyThemes: ['Joy', 'Partnership', 'Humility', 'Contentment', 'Christ'],
    difficulty: 'beginner',
    estimatedTime: 4
  },
  {
    id: 'colossians',
    name: 'Colossians',
    slug: 'colossians',
    testament: 'new',
    chapters: 4,
    description: 'The supremacy of Christ over all things',
    keyThemes: ['Supremacy', 'Fullness', 'New Life', 'Wisdom', 'Christ'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: '1-thessalonians',
    name: '1 Thessalonians',
    slug: '1-thessalonians',
    testament: 'new',
    chapters: 5,
    description: 'Living in light of Christ\'s return',
    keyThemes: ['Second Coming', 'Hope', 'Holiness', 'Encouragement', 'Love'],
    difficulty: 'beginner',
    estimatedTime: 4
  },
  {
    id: '2-thessalonians',
    name: '2 Thessalonians',
    slug: '2-thessalonians',
    testament: 'new',
    chapters: 3,
    description: 'Correcting misconceptions about Christ\'s return',
    keyThemes: ['Second Coming', 'Persecution', 'Work', 'Prayer', 'Discipline'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: '1-timothy',
    name: '1 Timothy',
    slug: '1-timothy',
    testament: 'new',
    chapters: 6,
    description: 'Instructions for church leadership and sound doctrine',
    keyThemes: ['Leadership', 'Doctrine', 'Conduct', 'Prayer', 'Contentment'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: '2-timothy',
    name: '2 Timothy',
    slug: '2-timothy',
    testament: 'new',
    chapters: 4,
    description: 'Paul\'s final charge to Timothy',
    keyThemes: ['Perseverance', 'Scripture', 'Suffering', 'Ministry', 'Faithfulness'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: 'titus',
    name: 'Titus',
    slug: 'titus',
    testament: 'new',
    chapters: 3,
    description: 'Organizing the church and living godly lives',
    keyThemes: ['Good Works', 'Leadership', 'Grace', 'Salvation', 'Godliness'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: 'philemon',
    name: 'Philemon',
    slug: 'philemon',
    testament: 'new',
    chapters: 1,
    description: 'A personal appeal for forgiveness and reconciliation',
    keyThemes: ['Forgiveness', 'Reconciliation', 'Love', 'Brotherhood', 'Grace'],
    difficulty: 'beginner',
    estimatedTime: 2
  },
  {
    id: 'hebrews',
    name: 'Hebrews',
    slug: 'hebrews',
    testament: 'new',
    chapters: 13,
    description: 'The superiority of Christ and the new covenant',
    keyThemes: ['Superiority', 'Faith', 'Priesthood', 'Covenant', 'Perseverance'],
    difficulty: 'advanced',
    estimatedTime: 5
  },
  {
    id: 'james',
    name: 'James',
    slug: 'james',
    testament: 'new',
    chapters: 5,
    description: 'Practical faith that works',
    keyThemes: ['Faith', 'Works', 'Trials', 'Wisdom', 'Speech'],
    difficulty: 'beginner',
    estimatedTime: 4
  },
  {
    id: '1-peter',
    name: '1 Peter',
    slug: '1-peter',
    testament: 'new',
    chapters: 5,
    description: 'Hope and holiness in the midst of suffering',
    keyThemes: ['Hope', 'Suffering', 'Holiness', 'Submission', 'Salvation'],
    difficulty: 'intermediate',
    estimatedTime: 4
  },
  {
    id: '2-peter',
    name: '2 Peter',
    slug: '2-peter',
    testament: 'new',
    chapters: 3,
    description: 'Growing in grace and knowledge while avoiding false teachers',
    keyThemes: ['Growth', 'Knowledge', 'False Teachers', 'Second Coming', 'Scripture'],
    difficulty: 'intermediate',
    estimatedTime: 3
  },
  {
    id: '1-john',
    name: '1 John',
    slug: '1-john',
    testament: 'new',
    chapters: 5,
    description: 'Fellowship with God through love and obedience',
    keyThemes: ['Fellowship', 'Love', 'Light', 'Obedience', 'Assurance'],
    difficulty: 'beginner',
    estimatedTime: 4
  },
  {
    id: '2-john',
    name: '2 John',
    slug: '2-john',
    testament: 'new',
    chapters: 1,
    description: 'Walking in truth and love while avoiding false teachers',
    keyThemes: ['Truth', 'Love', 'Commandments', 'False Teachers', 'Fellowship'],
    difficulty: 'intermediate',
    estimatedTime: 2
  },
  {
    id: '3-john',
    name: '3 John',
    slug: '3-john',
    testament: 'new',
    chapters: 1,
    description: 'Hospitality and support for fellow workers',
    keyThemes: ['Hospitality', 'Truth', 'Fellowship', 'Leadership', 'Example'],
    difficulty: 'intermediate',
    estimatedTime: 2
  },
  {
    id: 'jude',
    name: 'Jude',
    slug: 'jude',
    testament: 'new',
    chapters: 1,
    description: 'Contending for the faith against false teachers',
    keyThemes: ['Contending', 'Faith', 'False Teachers', 'Judgment', 'Perseverance'],
    difficulty: 'intermediate',
    estimatedTime: 2
  },
  {
    id: 'revelation',
    name: 'Revelation',
    slug: 'revelation',
    testament: 'new',
    chapters: 22,
    description: 'The revelation of Jesus Christ and the ultimate victory',
    keyThemes: ['Revelation', 'Victory', 'Judgment', 'New Creation', 'Worship'],
    difficulty: 'advanced',
    estimatedTime: 6
  }
];

// Helper functions for Bible book data
export const getBibleBook = (slug: string): BibleBook | undefined => {
  return BIBLE_BOOKS.find(book => book.slug === slug);
};

export const getOldTestamentBooks = (): BibleBook[] => {
  return BIBLE_BOOKS.filter(book => book.testament === 'old');
};

export const getNewTestamentBooks = (): BibleBook[] => {
  return BIBLE_BOOKS.filter(book => book.testament === 'new');
};

export const getBooksByDifficulty = (difficulty: 'beginner' | 'intermediate' | 'advanced'): BibleBook[] => {
  return BIBLE_BOOKS.filter(book => book.difficulty === difficulty);
};

export const getTotalChapters = (): number => {
  return BIBLE_BOOKS.reduce((total, book) => total + book.chapters, 0);
};

export const getBookChapterRange = (bookSlug: string): number[] => {
  const book = getBibleBook(bookSlug);
  if (!book) return [];
  return Array.from({ length: book.chapters }, (_, i) => i + 1);
};

// Generate all possible chapter URLs for static generation
export const getAllChapterSlugs = (): { book: string; chapter: string }[] => {
  const slugs: { book: string; chapter: string }[] = [];
  
  BIBLE_BOOKS.forEach(book => {
    for (let chapter = 1; chapter <= book.chapters; chapter++) {
      slugs.push({
        book: book.slug,
        chapter: chapter.toString()
      });
    }
  });
  
  return slugs;
};

// Constants for the comprehensive platform
export const PLATFORM_STATS = {
  TOTAL_BOOKS: BIBLE_BOOKS.length,
  TOTAL_CHAPTERS: getTotalChapters(),
  OLD_TESTAMENT_BOOKS: getOldTestamentBooks().length,
  NEW_TESTAMENT_BOOKS: getNewTestamentBooks().length,
  ESTIMATED_QUIZZES: getTotalChapters() + BIBLE_BOOKS.length, // Chapter quizzes + book quizzes
} as const;