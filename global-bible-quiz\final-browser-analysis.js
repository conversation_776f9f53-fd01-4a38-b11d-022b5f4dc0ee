const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

async function finalBrowserAnalysis() {
  console.log('🔍 COMPREHENSIVE BIBLE QUIZ APPLICATION ANALYSIS');
  console.log('================================================');
  
  const testResults = {
    timestamp: new Date().toISOString(),
    accessiblePort: 3000,
    applicationStatus: 'running',
    mainPageAnalysis: {},
    quizPageAnalysis: {},
    hydrationIssueAnalysis: {},
    functionalityTests: {},
    recommendations: []
  };

  const baseUrl = 'http://localhost:3000';

  try {
    // Analyze main page
    console.log('\n📄 ANALYZING MAIN PAGE...');
    const mainPageContent = await runCommand(`curl -s "${baseUrl}"`);
    
    testResults.mainPageAnalysis = {
      contentLength: mainPageContent.length,
      hasTitle: mainPageContent.includes('<title>'),
      hasNextJs: mainPageContent.includes('_next') || mainPageContent.includes('__next'),
      hasReactElements: mainPageContent.includes('data-reactroot') || mainPageContent.includes('__NEXT_DATA__'),
      hasQuizLinks: (mainPageContent.match(/href="[^"]*quiz[^"]*"/gi) || []).length,
      hasInteractiveElements: mainPageContent.includes('button') || mainPageContent.includes('input'),
      pageTitle: extractTitle(mainPageContent),
      metaDescription: extractMetaDescription(mainPageContent),
      hasAccessibilityFeatures: mainPageContent.includes('aria-') || mainPageContent.includes('role='),
      hasPWAFeatures: mainPageContent.includes('manifest.json')
    };

    console.log(`✅ Main page loaded: ${testResults.mainPageAnalysis.contentLength} characters`);
    console.log(`📋 Title: ${testResults.mainPageAnalysis.pageTitle}`);
    console.log(`🔗 Quiz links found: ${testResults.mainPageAnalysis.hasQuizLinks}`);
    console.log(`⚡ Interactive elements: ${testResults.mainPageAnalysis.hasInteractiveElements}`);

    // Analyze Genesis 1 Quiz page
    console.log('\n🎮 ANALYZING GENESIS 1 QUIZ PAGE...');
    const genesisQuizContent = await runCommand(`curl -s "${baseUrl}/genesis-1-quiz"`);
    
    testResults.quizPageAnalysis = {
      contentLength: genesisQuizContent.length,
      questionsFound: (genesisQuizContent.match(/\d+\.\s+.*\?/g) || []).length,
      radioInputsFound: (genesisQuizContent.match(/type="radio"/g) || []).length,
      hasProgressBar: genesisQuizContent.includes('progress') || genesisQuizContent.includes('Progress'),
      hasTimer: genesisQuizContent.includes('timer') || genesisQuizContent.includes('Timer'),
      hasScriptureReferences: genesisQuizContent.includes('Genesis') && genesisQuizContent.includes(':'),
      hasInstructions: genesisQuizContent.includes('Choose the best answer') || genesisQuizContent.includes('instructions'),
      estimatedQuestions: Math.floor((genesisQuizContent.match(/name="gen1-q\d+"/g) || []).length / 4), // Assuming 4 options per question
      hasInteractiveQuiz: genesisQuizContent.includes('InteractiveQuiz') || genesisQuizContent.includes('quiz-')
    };

    console.log(`📝 Questions identified: ${testResults.quizPageAnalysis.questionsFound}`);
    console.log(`🔘 Radio inputs: ${testResults.quizPageAnalysis.radioInputsFound}`);
    console.log(`📊 Has progress bar: ${testResults.quizPageAnalysis.hasProgressBar}`);
    console.log(`⏰ Has timer: ${testResults.quizPageAnalysis.hasTimer}`);
    console.log(`📖 Has scripture references: ${testResults.quizPageAnalysis.hasScriptureReferences}`);

    // Test bypass parameter
    console.log('\n🚀 TESTING BYPASS PARAMETER...');
    const bypassContent = await runCommand(`curl -s "${baseUrl}/genesis-1-quiz?start=true"`);
    
    testResults.functionalityTests.bypassParameter = {
      originalLength: genesisQuizContent.length,
      bypassLength: bypassContent.length,
      isDifferent: bypassContent !== genesisQuizContent,
      working: bypassContent.length > 0 && bypassContent !== genesisQuizContent
    };

    console.log(`🔄 Bypass parameter working: ${testResults.functionalityTests.bypassParameter.working}`);
    console.log(`📏 Content difference: ${testResults.functionalityTests.bypassParameter.bypassLength - testResults.functionalityTests.bypassParameter.originalLength} characters`);

    // Analyze client-side JavaScript
    console.log('\n⚛️  ANALYZING CLIENT-SIDE JAVASCRIPT...');
    const jsChunks = extractJavaScriptChunks(genesisQuizContent);
    
    testResults.hydrationIssueAnalysis = {
      hasNextJsChunks: jsChunks.includes('_next/static/chunks/'),
      hasAppChunks: jsChunks.includes('app/'),
      hasHydrationCode: genesisQuizContent.includes('__next_f') || genesisQuizContent.includes('self.__next_f'),
      hasInteractiveComponents: genesisQuizContent.includes('InteractiveQuiz') || genesisQuizContent.includes('client'),
      hasDynamicImports: genesisQuizContent.includes('dynamic') || genesisQuizContent.includes('import('),
      hasSSRDisabled: genesisQuizContent.includes('ssr: false') || genesisQuizContent.includes('ssr:false'),
      suppressHydrationWarning: genesisQuizContent.includes('suppressHydrationWarning')
    };

    console.log(`🔧 Next.js chunks found: ${testResults.hydrationIssueAnalysis.hasNextJsChunks}`);
    console.log(`⚡ Interactive components detected: ${testResults.hydrationIssueAnalysis.hasInteractiveComponents}`);
    console.log(`🚫 SSR disabled patterns: ${testResults.hydrationIssueAnalysis.hasSSRDisabled}`);
    console.log(`⚠️  Hydration warnings suppressed: ${testResults.hydrationIssueAnalysis.suppressHydrationWarning}`);

    // Test other quiz pages
    console.log('\n📚 TESTING OTHER QUIZ PAGES...');
    const otherPages = ['/matthew-quiz', '/bible-quizzes', '/genesis-chapters'];
    
    for (const pagePath of otherPages) {
      try {
        const pageContent = await runCommand(`curl -s "${baseUrl}${pagePath}"`);
        testResults.functionalityTests[`page_${pagePath.replace(/[^a-zA-Z0-9]/g, '_')}`] = {
          accessible: pageContent.length > 1000,
          contentLength: pageContent.length,
          hasQuizElements: pageContent.includes('quiz') || pageContent.includes('Quiz'),
          hasInteractiveElements: pageContent.includes('button') || pageContent.includes('input'),
          title: extractTitle(pageContent)
        };
        console.log(`✅ ${pagePath}: ${pageContent.length} characters, quiz elements: ${pageContent.includes('quiz')}`);
      } catch (error) {
        testResults.functionalityTests[`page_${pagePath.replace(/[^a-zA-Z0-9]/g, '_')}_error`] = error.message;
        console.log(`❌ ${pagePath}: Error - ${error.message}`);
      }
    }

    // Generate recommendations
    console.log('\n💡 GENERATING RECOMMENDATIONS...');
    
    if (!testResults.quizPageAnalysis.hasTimer) {
      testResults.recommendations.push('Consider adding a visible timer component to quiz pages for better user experience');
    }
    
    if (testResults.hydrationIssueAnalysis.hasInteractiveComponents && !testResults.hydrationIssueAnalysis.hasSSRDisabled) {
      testResults.recommendations.push('Interactive quiz components may benefit from dynamic imports with SSR disabled to prevent hydration issues');
    }
    
    if (testResults.quizPageAnalysis.radioInputsFound > 0 && !testResults.quizPageAnalysis.hasInteractiveQuiz) {
      testResults.recommendations.push('Quiz interactions appear to be server-rendered; consider client-side state management for better interactivity');
    }
    
    if (!testResults.mainPageAnalysis.hasAccessibilityFeatures) {
      testResults.recommendations.push('Add more ARIA labels and accessibility features for better screen reader support');
    }

    testResults.recommendations.push('Monitor browser console for hydration warnings during client-side interactions');
    testResults.recommendations.push('Test quiz submission and results display functionality with browser DevTools open');

    // Final summary
    console.log('\n📊 FINAL TEST SUMMARY');
    console.log('==================');
    console.log(`✅ Application Status: ${testResults.applicationStatus.toUpperCase()}`);
    console.log(`🌐 Accessible Port: ${testResults.accessiblePort}`);
    console.log(`📄 Main Page: ${testResults.mainPageAnalysis.contentLength} chars, ${testResults.mainPageAnalysis.hasQuizLinks} quiz links`);
    console.log(`🎮 Genesis Quiz: ${testResults.quizPageAnalysis.questionsFound} questions, ${testResults.quizPageAnalysis.radioInputsFound} inputs`);
    console.log(`🚀 Bypass Parameter: ${testResults.functionalityTests.bypassParameter.working ? 'WORKING' : 'NOT WORKING'}`);
    console.log(`⚛️  Hydration Setup: ${testResults.hydrationIssueAnalysis.hasInteractiveComponents ? 'Interactive components detected' : 'Static rendering'}`);
    console.log(`💡 Recommendations: ${testResults.recommendations.length} suggestions generated`);

    console.log('\n🔍 KEY FINDINGS:');
    testResults.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });

  } catch (error) {
    console.error('❌ Analysis failed:', error);
    testResults.error = error.message;
  }

  // Save results
  fs.writeFileSync(
    path.join(__dirname, 'final-analysis-results.json'),
    JSON.stringify(testResults, null, 2)
  );

  return testResults;
}

function extractTitle(html) {
  const titleMatch = html.match(/<title>(.*?)<\/title>/i);
  return titleMatch ? titleMatch[1] : 'No title found';
}

function extractMetaDescription(html) {
  const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i);
  return descMatch ? descMatch[1] : 'No description found';
}

function extractJavaScriptChunks(html) {
  const scriptMatches = html.match(/<script[^>]*src=["']([^"']*)/gi) || [];
  return scriptMatches.join(' ');
}

function runCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error && !stdout) {
        reject(error);
      } else {
        resolve(stdout || stderr);
      }
    });
  });
}

// Run the analysis
finalBrowserAnalysis().then(results => {
  console.log('\n✅ Final browser analysis completed!');
  console.log('📄 Detailed results saved to final-analysis-results.json');
}).catch(error => {
  console.error('❌ Analysis failed:', error);
});

module.exports = { finalBrowserAnalysis };