'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\n// Genesis Chapter 1 Quiz - 18 questions about Creation (LINEAR FORMAT)\nconst GENESIS_1_QUESTIONS = [\n  {\n    id: 1,\n    question: \"What did God create on the first day?\",\n    options: [\"Light\", \"The heavens and earth\", \"Plants\", \"Animals\"],\n    correctAnswer: 0,\n    explanation: \"On the first day, God said 'Let there be light,' and there was light. He separated the light from the darkness.\",\n    verseReference: \"Genesis 1:3-5\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 2,\n    question: \"What did God call the light?\",\n    options: [\"Morning\", \"Day\", \"Sun\", \"Brightness\"],\n    correctAnswer: 1,\n    explanation: \"God called the light Day, and the darkness He called Night.\",\n    verseReference: \"Genesis 1:5\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 3,\n    question: \"What did God create on the second day?\",\n    options: [\"Land and seas\", \"Firmament (sky)\", \"Sun and moon\", \"Plants\"],\n    correctAnswer: 1,\n    explanation: \"God made the firmament, and divided the waters which were under the firmament from the waters which were above the firmament.\",\n    verseReference: \"Genesis 1:6-8\",\n    difficulty: \"medium\"\n  },\n  {\n    id: 4,\n    question: \"What did God call the firmament?\",\n    options: [\"Sky\", \"Heaven\", \"Atmosphere\", \"Space\"],\n    correctAnswer: 1,\n    explanation: \"And God called the firmament Heaven.\",\n    verseReference: \"Genesis 1:8\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 5,\n    question: \"On which day did God create dry land?\",\n    options: [\"Second day\", \"Third day\", \"Fourth day\", \"Fifth day\"],\n    correctAnswer: 1,\n    explanation: \"On the third day, God gathered the waters together and let the dry land appear.\",\n    verseReference: \"Genesis 1:9-10\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 6,\n    question: \"What did God call the dry land?\",\n    options: [\"Ground\", \"Earth\", \"Land\", \"Soil\"],\n    correctAnswer: 1,\n    explanation: \"And God called the dry land Earth, and the gathering together of the waters He called Seas.\",\n    verseReference: \"Genesis 1:10\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 7,\n    question: \"What else did God create on the third day besides dry land?\",\n    options: [\"Animals\", \"Fish\", \"Plants\", \"Stars\"],\n    correctAnswer: 2,\n    explanation: \"On the third day, God also created grass, herbs, and fruit trees that yield fruit according to their kind.\",\n    verseReference: \"Genesis 1:11-13\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 8,\n    question: \"What did God create on the fourth day?\",\n    options: [\"Birds and fish\", \"Sun, moon, and stars\", \"Land animals\", \"Humans\"],\n    correctAnswer: 1,\n    explanation: \"On the fourth day, God made two great lights: the greater light to rule the day and the lesser light to rule the night, and the stars also.\",\n    verseReference: \"Genesis 1:14-19\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 9,\n    question: \"What are the purposes of the lights in the firmament?\",\n    options: [\"For beauty only\", \"To divide day and night, for signs, seasons, days, and years\", \"To provide heat\", \"To create weather\"],\n    correctAnswer: 1,\n    explanation: \"God said the lights would be for signs and seasons, and for days and years, and to divide the day from the night.\",\n    verseReference: \"Genesis 1:14\",\n    difficulty: \"medium\"\n  },\n  {\n    id: 10,\n    question: \"What did God create on the fifth day?\",\n    options: [\"Land animals\", \"Birds and sea creatures\", \"Plants\", \"Humans\"],\n    correctAnswer: 1,\n    explanation: \"On the fifth day, God created great sea creatures and every living thing that moves in the waters, and every winged bird.\",\n    verseReference: \"Genesis 1:20-23\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 11,\n    question: \"What was the first blessing God gave?\",\n    options: [\"To the light\", \"To the plants\", \"To the sea creatures and birds\", \"To the land animals\"],\n    correctAnswer: 2,\n    explanation: \"God blessed the sea creatures and birds, saying 'Be fruitful and multiply.'\",\n    verseReference: \"Genesis 1:22\",\n    difficulty: \"medium\"\n  },\n  {\n    id: 12,\n    question: \"What did God create on the sixth day first?\",\n    options: [\"Humans\", \"Land animals\", \"More plants\", \"Nothing else\"],\n    correctAnswer: 1,\n    explanation: \"On the sixth day, God first made the living creatures of the earth: cattle, creeping things, and beasts of the earth.\",\n    verseReference: \"Genesis 1:24-25\",\n    difficulty: \"medium\"\n  },\n  {\n    id: 13,\n    question: \"In whose image did God create man?\",\n    options: [\"Angels' image\", \"Animals' image\", \"God's own image\", \"No specific image\"],\n    correctAnswer: 2,\n    explanation: \"God said, 'Let Us make man in Our image, according to Our likeness.'\",\n    verseReference: \"Genesis 1:26-27\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 14,\n    question: \"What dominion did God give to humans?\",\n    options: [\"Over the earth only\", \"Over fish, birds, and every living thing\", \"Over plants only\", \"Over nothing\"],\n    correctAnswer: 1,\n    explanation: \"God gave humans dominion over the fish of the sea, over the birds of the air, and over every living thing that moves on the earth.\",\n    verseReference: \"Genesis 1:28\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 15,\n    question: \"What did God give humans and animals for food?\",\n    options: [\"Meat only\", \"Plants and herbs\", \"Fish only\", \"Nothing specified\"],\n    correctAnswer: 1,\n    explanation: \"God gave every herb that yields seed and every tree whose fruit yields seed for food to both humans and animals.\",\n    verseReference: \"Genesis 1:29-30\",\n    difficulty: \"medium\"\n  },\n  {\n    id: 16,\n    question: \"How did God view His creation?\",\n    options: [\"It was acceptable\", \"It was very good\", \"It was perfect\", \"It needed improvement\"],\n    correctAnswer: 1,\n    explanation: \"God saw everything that He had made, and indeed it was very good.\",\n    verseReference: \"Genesis 1:31\",\n    difficulty: \"easy\"\n  },\n  {\n    id: 17,\n    question: \"How many times does the phrase 'And God saw that it was good' appear in chapter 1?\",\n    options: [\"5 times\", \"6 times\", \"7 times\", \"8 times\"],\n    correctAnswer: 2,\n    explanation: \"The phrase 'And God saw that it was good' appears 7 times in Genesis 1, emphasizing the perfection of God's creation.\",\n    verseReference: \"Genesis 1:4, 10, 12, 18, 21, 25, 31\",\n    difficulty: \"hard\"\n  },\n  {\n    id: 18,\n    question: \"What marked the end of the sixth day?\",\n    options: [\"God rested\", \"Evening and morning\", \"The creation stopped\", \"Animals were named\"],\n    correctAnswer: 1,\n    explanation: \"So the evening and the morning were the sixth day, completing God's work of creation.\",\n    verseReference: \"Genesis 1:31\",\n    difficulty: \"easy\"\n  }\n];\n\nexport default function Genesis1QuizPage() {\n  const [selectedAnswers, setSelectedAnswers] = useState<number[]>(new Array(GENESIS_1_QUESTIONS.length).fill(-1));\n  const [showResults, setShowResults] = useState(false);\n  const [score, setScore] = useState(0);\n\n  const handleAnswerSelect = (questionIndex: number, answerIndex: number) => {\n    const newAnswers = [...selectedAnswers];\n    newAnswers[questionIndex] = answerIndex;\n    setSelectedAnswers(newAnswers);\n  };\n\n  const handleSubmitQuiz = () => {\n    // Check if all questions are answered\n    const unansweredQuestions = selectedAnswers.filter(answer => answer === -1).length;\n    if (unansweredQuestions > 0) {\n      alert(`Please answer all questions. You have ${unansweredQuestions} unanswered questions.`);\n      return;\n    }\n\n    // Calculate score\n    let correct = 0;\n    GENESIS_1_QUESTIONS.forEach((question, index) => {\n      if (selectedAnswers[index] === question.correctAnswer) {\n        correct++;\n      }\n    });\n    setScore(correct);\n    setShowResults(true);\n    \n    // Scroll to results section\n    setTimeout(() => {\n      const resultsElement = document.getElementById('quiz-results');\n      if (resultsElement) {\n        resultsElement.scrollIntoView({ behavior: 'smooth' });\n      }\n    }, 100);\n  };\n\n  const getScoreMessage = () => {\n    const percentage = Math.round((score / GENESIS_1_QUESTIONS.length) * 100);\n    if (percentage >= 90) return \"Outstanding! You know Genesis 1 very well!\";\n    if (percentage >= 80) return \"Excellent! You understand the creation account!\";\n    if (percentage >= 70) return \"Good job! Keep studying creation!\";\n    if (percentage >= 60) return \"Not bad! Review Genesis 1 for better results.\";\n    return \"Keep learning! Study the creation account more closely.\";\n  };\n\n  const resetQuiz = () => {\n    setSelectedAnswers(new Array(GENESIS_1_QUESTIONS.length).fill(-1));\n    setShowResults(false);\n    setScore(0);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-b from-green-50 to-white py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className=\"mb-4\">\n            <Link href=\"/genesis-chapters\" className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n              ← Back to Genesis Chapters\n            </Link>\n          </div>\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Genesis Chapter 1 Quiz</h1>\n          <p className=\"text-lg text-gray-600 mb-6\">\n            Creation of the Universe - Test your knowledge of the creation account\n          </p>\n          \n          {/* Quiz Instructions */}\n          <div className=\"bg-white rounded-lg p-6 shadow-sm mb-8\">\n            <p className=\"text-gray-700 mb-4\">\n              Answer all questions below and click \"Submit Quiz and See Results\" when you're finished.\n              Each question is designed to test your understanding of Genesis Chapter 1.\n            </p>\n            <div className=\"flex items-center justify-center gap-6 text-sm text-gray-600\">\n              <span>📊 {GENESIS_1_QUESTIONS.length} Questions</span>\n              <span>⏱️ ~5-8 Minutes</span>\n              <span>📖 Genesis 1:1-31</span>\n            </div>\n          </div>\n        </div>\n\n        {/* All Questions - Linear Format */}\n        <div className=\"space-y-8 mb-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Quiz Questions</h2>\n          \n          {GENESIS_1_QUESTIONS.map((question, questionIndex) => (\n            <div key={question.id} className=\"bg-white rounded-xl shadow-lg p-6 border border-gray-100\">\n              <div className=\"mb-4\">\n                <div className=\"flex items-center gap-2 mb-3\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    question.difficulty === 'easy' ? 'bg-green-100 text-green-800' :\n                    question.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-red-100 text-red-800'\n                  }`}>\n                    {question.difficulty}\n                  </span>\n                  <span className=\"text-sm text-gray-500\">{question.verseReference}</span>\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  {questionIndex + 1}. {question.question}\n                </h3>\n              </div>\n\n              <div className=\"space-y-2\">\n                {question.options.map((option, optionIndex) => (\n                  <button\n                    key={optionIndex}\n                    onClick={() => handleAnswerSelect(questionIndex, optionIndex)}\n                    className={`w-full text-left p-3 rounded-lg border-2 transition-all duration-200 ${\n                      selectedAnswers[questionIndex] === optionIndex\n                        ? 'border-green-500 bg-green-50 text-green-900'\n                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n                    }`}\n                  >\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${\n                        selectedAnswers[questionIndex] === optionIndex\n                          ? 'border-green-500 bg-green-500'\n                          : 'border-gray-300'\n                      }`}>\n                        {selectedAnswers[questionIndex] === optionIndex && (\n                          <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                        )}\n                      </div>\n                      <span className=\"text-sm font-medium\">\n                        {String.fromCharCode(65 + optionIndex)}. {option}\n                      </span>\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"text-center mb-12\">\n          <button\n            onClick={handleSubmitQuiz}\n            className=\"px-8 py-4 bg-green-600 text-white text-lg font-semibold rounded-lg hover:bg-green-700 transition-colors shadow-lg\"\n          >\n            Submit Quiz and See Results\n          </button>\n          <p className=\"text-sm text-gray-600 mt-2\">\n            Make sure you've answered all questions before submitting\n          </p>\n        </div>\n\n        {/* Results Section */}\n        {showResults && (\n          <div id=\"quiz-results\" className=\"space-y-8\">\n            {/* Score Display */}\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 text-center\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Your Results</h2>\n              <div className=\"text-6xl font-bold text-green-600 mb-4\">\n                {Math.round((score / GENESIS_1_QUESTIONS.length) * 100)}%\n              </div>\n              <p className=\"text-xl text-gray-700 mb-4\">\n                You scored {score} out of {GENESIS_1_QUESTIONS.length} questions correctly\n              </p>\n              <p className=\"text-lg text-green-600 font-semibold\">{getScoreMessage()}</p>\n            </div>\n\n            {/* Detailed Results */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Question Breakdown</h3>\n              <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n                {GENESIS_1_QUESTIONS.map((question, index) => {\n                  const isCorrect = selectedAnswers[index] === question.correctAnswer;\n                  return (\n                    <div key={question.id} className={`p-4 rounded-lg border-2 ${\n                      isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                    }`}>\n                      <div className=\"flex items-start gap-3\">\n                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold ${\n                          isCorrect ? 'bg-green-500' : 'bg-red-500'\n                        }`}>\n                          {isCorrect ? '✓' : '✗'}\n                        </div>\n                        <div className=\"flex-1\">\n                          <p className=\"font-semibold text-gray-900 mb-2\">\n                            {index + 1}. {question.question}\n                          </p>\n                          <p className=\"text-sm text-gray-600 mb-1\">\n                            Your answer: {question.options[selectedAnswers[index]]}\n                          </p>\n                          {!isCorrect && (\n                            <p className=\"text-sm text-green-700 mb-1\">\n                              Correct answer: {question.options[question.correctAnswer]}\n                            </p>\n                          )}\n                          <p className=\"text-xs text-gray-500 italic\">\n                            {question.explanation} ({question.verseReference})\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* MANDATORY Internal Links Section */}\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-4\">Continue Your Bible Study Journey</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                <Link href=\"/genesis-1-study\" className=\"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                  📖 Study Genesis Chapter 1 First\n                </Link>\n                <Link href=\"/genesis-2-quiz\" className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                  → Genesis Chapter 2 Quiz\n                </Link>\n                <Link href=\"/genesis-quiz\" className=\"inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\">\n                  🎯 Take the Complete Genesis Quiz\n                </Link>\n                <Link href=\"/adam-quiz\" className=\"inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n                  👤 Adam Character Study\n                </Link>\n                <Link href=\"/creation-quiz\" className=\"inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors\">\n                  🌍 Creation Theme Quiz\n                </Link>\n                <Link href=\"/bible-quizzes\" className=\"inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\">\n                  🏠 Browse All Bible Quizzes\n                </Link>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"text-center space-x-4\">\n              <button \n                onClick={resetQuiz}\n                className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                Retake Quiz\n              </button>\n              <Link \n                href=\"/genesis-chapters\" \n                className=\"inline-block px-6 py-3 border-2 border-green-600 text-green-600 rounded-lg hover:bg-green-50 transition-colors\"\n              >\n                More Genesis Chapters\n              </Link>\n            </div>\n          </div>\n        )}\n\n        {/* Study Resources */}\n        <div className=\"bg-white rounded-xl shadow-lg p-6 mt-12\">\n          <div className=\"grid lg:grid-cols-2 gap-6 items-center\">\n            <div className=\"relative\">\n              <div className=\"relative rounded-lg overflow-hidden shadow-md\">\n                <Image\n                  src=\"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n                  alt=\"Hands in prayer over old Bible\"\n                  width={400}\n                  height={250}\n                  className=\"w-full h-auto object-cover\"\n                />\n              </div>\n            </div>\n            <div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Study Genesis 1: The Foundation of Faith\n              </h3>\n              <p className=\"text-gray-600 text-sm mb-4\">\n                Genesis 1 establishes God as the Creator of all things. The systematic creation over six days \n                reveals God's power, wisdom, and design. Understanding creation helps us grasp our purpose and \n                relationship with our Creator.\n              </p>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-start gap-2\">\n                  <div className=\"w-1.5 h-1.5 bg-green-600 rounded-full mt-2\"></div>\n                  <p className=\"text-sm text-gray-700\">Each day builds upon the previous, showing God's orderly plan</p>\n                </div>\n                <div className=\"flex items-start gap-2\">\n                  <div className=\"w-1.5 h-1.5 bg-green-600 rounded-full mt-2\"></div>\n                  <p className=\"text-sm text-gray-700\">Humans are created in God's image with special purpose</p>\n                </div>\n                <div className=\"flex items-start gap-2\">\n                  <div className=\"w-1.5 h-1.5 bg-green-600 rounded-full mt-2\"></div>\n                  <p className=\"text-sm text-gray-700\">All creation was declared \"very good\" by God</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}