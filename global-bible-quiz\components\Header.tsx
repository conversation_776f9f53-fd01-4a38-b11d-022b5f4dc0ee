'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function Header() {
  const [isQuizDropdownOpen, setIsQuizDropdownOpen] = useState(false);
  const [isCharactersDropdownOpen, setIsCharactersDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleDropdownToggle = (dropdown: 'quiz' | 'characters') => {
    if (dropdown === 'quiz') {
      setIsQuizDropdownOpen(!isQuizDropdownOpen);
      setIsCharactersDropdownOpen(false);
    } else {
      setIsCharactersDropdownOpen(!isCharactersDropdownOpen);
      setIsQuizDropdownOpen(false);
    }
  };

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
            <div className="relative w-8 h-8">
              <Image
                src="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png"
                alt="Global Bible Quiz Logo"
                width={32}
                height={32}
                className="w-8 h-8 object-cover rounded"
              />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Global Bible Quiz</h1>
              <p className="text-xs text-gray-600 hidden sm:block">Master Scripture Knowledge</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Home
            </Link>

            {/* Bible Quizzes Dropdown */}
            <div className="relative">
              <button
                onClick={() => handleDropdownToggle('quiz')}
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-colors"
              >
                Bible Quizzes
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {isQuizDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50">
                  <Link href="/bible-quizzes" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    📚 All Bible Books
                  </Link>
                  <Link href="/genesis-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    📖 Genesis Quiz
                  </Link>
                  <Link href="/exodus-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    📖 Exodus Quiz
                  </Link>
                  <Link href="/leviticus-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    📖 Leviticus Quiz
                  </Link>
                  <Link href="/matthew-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    📖 Matthew Quiz
                  </Link>
                  <Link href="/john-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    📖 John Quiz
                  </Link>
                  <div className="border-t border-gray-200 my-2"></div>
                  <Link href="/random-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    🎲 Random Quiz
                  </Link>
                </div>
              )}
            </div>

            <Link href="/study-guides" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Study Guides
            </Link>

            {/* Characters Dropdown */}
            <div className="relative">
              <button
                onClick={() => handleDropdownToggle('characters')}
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-colors"
              >
                Characters
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {isCharactersDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50">
                  <Link href="/characters" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    👥 All Characters
                  </Link>
                  <Link href="/abraham-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    👤 Abraham
                  </Link>
                  <Link href="/moses-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    👤 Moses
                  </Link>
                  <Link href="/david-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    👤 David
                  </Link>
                  <Link href="/jesus-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    👤 Jesus Christ
                  </Link>
                  <Link href="/paul-quiz" className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    👤 Paul
                  </Link>
                </div>
              )}
            </div>

            <Link href="/resources" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Resources
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              About
            </Link>
            <Link href="/login" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Login
            </Link>
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={handleMobileMenuToggle}
            className="lg:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu - 70% Width Constraint */}
        {isMobileMenuOpen && (
          <div className="lg:hidden absolute top-16 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40">
            <div className="max-w-7xl mx-auto px-4 py-4" style={{ maxWidth: '70vw' }}>
              <nav className="space-y-4">
                <Link 
                  href="/" 
                  onClick={closeMobileMenu}
                  className="block text-gray-700 hover:text-blue-600 font-medium py-2"
                >
                  🏠 Home
                </Link>
                
                <div className="space-y-2">
                  <div className="text-gray-900 font-semibold">📚 Bible Quizzes</div>
                  <div className="ml-4 space-y-2">
                    <Link href="/bible-quizzes" onClick={closeMobileMenu} className="block text-sm text-gray-600 hover:text-blue-600 py-1">
                      All Bible Books
                    </Link>
                    <Link href="/genesis-quiz" onClick={closeMobileMenu} className="block text-sm text-gray-600 hover:text-blue-600 py-1">
                      Genesis Quiz
                    </Link>
                    <Link href="/exodus-quiz" onClick={closeMobileMenu} className="block text-sm text-gray-600 hover:text-blue-600 py-1">
                      Exodus Quiz
                    </Link>
                  </div>
                </div>

                <Link 
                  href="/study-guides" 
                  onClick={closeMobileMenu}
                  className="block text-gray-700 hover:text-blue-600 font-medium py-2"
                >
                  📖 Study Guides
                </Link>

                <div className="space-y-2">
                  <div className="text-gray-900 font-semibold">👥 Characters</div>
                  <div className="ml-4 space-y-2">
                    <Link href="/characters" onClick={closeMobileMenu} className="block text-sm text-gray-600 hover:text-blue-600 py-1">
                      All Characters
                    </Link>
                    <Link href="/abraham-quiz" onClick={closeMobileMenu} className="block text-sm text-gray-600 hover:text-blue-600 py-1">
                      Abraham
                    </Link>
                    <Link href="/jesus-quiz" onClick={closeMobileMenu} className="block text-sm text-gray-600 hover:text-blue-600 py-1">
                      Jesus Christ
                    </Link>
                  </div>
                </div>

                <Link 
                  href="/resources" 
                  onClick={closeMobileMenu}
                  className="block text-gray-700 hover:text-blue-600 font-medium py-2"
                >
                  📋 Resources
                </Link>
                <Link 
                  href="/about" 
                  onClick={closeMobileMenu}
                  className="block text-gray-700 hover:text-blue-600 font-medium py-2"
                >
                  ℹ️ About
                </Link>
                <Link 
                  href="/login" 
                  onClick={closeMobileMenu}
                  className="block bg-blue-600 text-white px-4 py-2 rounded-lg text-center font-medium hover:bg-blue-700 transition-colors"
                >
                  🔐 Login
                </Link>
              </nav>
            </div>
          </div>
        )}
      </div>

      {/* Backdrop for mobile menu */}
      {isMobileMenuOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-25 z-30" 
          onClick={closeMobileMenu}
        ></div>
      )}
    </header>
  );
}