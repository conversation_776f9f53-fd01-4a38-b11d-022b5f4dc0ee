# Development Docker file with hot reload and auto-refresh
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies needed for building native modules
RUN apk add --no-cache libc6-compat

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source files
COPY . .

# Expose the port for development server
EXPOSE 3000

# Set environment variables for development
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV WATCHPACK_POLLING=true

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Change ownership to nextjs user
RUN chown -R nextjs:nodejs /app
USER nextjs

# Start the development server with hot reload
CMD ["npm", "run", "dev"]