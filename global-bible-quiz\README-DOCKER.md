# Global Bible Quiz - Docker Development Guide

This guide will help you set up and run the Global Bible Quiz application using Docker for development, testing, and deployment.

## 🚀 Quick Start

### Prerequisites

- **Docker Desktop** installed and running
- **Git** for version control
- **Make** (optional, for convenience commands)

### Development Setup

1. **Start Development Environment**
   ```bash
   # Using Make (recommended)
   make dev
   
   # Or using Docker Compose directly
   docker-compose -f docker-compose.dev.yml up --build
   ```

2. **Access the Application**
   - Development Server: http://localhost:3000
   - Auto-refresh enabled - changes reflect immediately

3. **View Logs**
   ```bash
   make dev-logs
   # Or: docker-compose -f docker-compose.dev.yml logs -f
   ```

## 📋 Available Commands

### Make Commands (Recommended)

```bash
make help          # Show all available commands
make dev           # Start development environment
make prod          # Start production environment  
make test          # Run all tests
make clean         # Clean up Docker resources
make deploy-aws    # Deploy to AWS
make deploy-do     # Deploy to Digital Ocean
```

### Development Commands

```bash
make dev           # Start dev server with hot reload
make dev-build     # Build development containers
make dev-logs      # Show development logs
make dev-stop      # Stop development environment
make restart-dev   # Restart development environment
```

### Production Commands

```bash
make prod          # Start production server
make prod-build    # Build production containers
make prod-logs     # Show production logs
make prod-stop     # Stop production environment
make restart-prod  # Restart production environment
```

### Testing Commands

```bash
make test          # Run all tests in Docker
make test-build    # Build test containers
```

## 🏗️ Docker Configuration

### Development Environment (`docker-compose.dev.yml`)

- **Hot Reload**: File changes automatically refresh the browser
- **Volume Mounting**: Source code mounted for instant updates
- **Port**: Accessible on `localhost:3000`
- **Environment**: `NODE_ENV=development`

### Production Environment (`docker-compose.prod.yml`)

- **Optimized Build**: Multi-stage build for smaller image size
- **Security**: Non-root user, minimal attack surface
- **Port**: Accessible on `localhost:80`
- **Environment**: `NODE_ENV=production`

## 🔧 File Structure

```
docker-scripts/
├── dev-start.sh           # Development startup script
├── prod-start.sh          # Production startup script
├── test-run.sh            # Test runner script
├── deploy-aws.sh          # AWS deployment script
└── deploy-digitalocean.sh # Digital Ocean deployment script

Dockerfile                 # Production Docker image
Dockerfile.dev             # Development Docker image
docker-compose.yml         # Main compose file
docker-compose.dev.yml     # Development configuration
docker-compose.prod.yml    # Production configuration
.dockerignore              # Files to exclude from Docker context
Makefile                   # Convenient command shortcuts
```

## 🌐 Deployment

### AWS Deployment

1. **Prerequisites**
   ```bash
   # Install AWS CLI
   pip install awscli
   
   # Configure AWS credentials
   aws configure
   ```

2. **Deploy**
   ```bash
   make deploy-aws
   ```

3. **Environment Variables**
   ```bash
   export AWS_REGION=us-east-1
   export ECR_REPOSITORY=global-bible-quiz
   export ECS_CLUSTER=bible-quiz-cluster
   export ECS_SERVICE=bible-quiz-service
   ```

### Digital Ocean Deployment

1. **Prerequisites**
   ```bash
   # Install doctl
   snap install doctl
   
   # Authenticate
   doctl auth init
   ```

2. **Deploy**
   ```bash
   make deploy-do
   ```

3. **Environment Variables**
   ```bash
   export DO_APP_NAME=global-bible-quiz
   export DO_REGION=nyc1
   export DOCKER_REGISTRY=registry.digitalocean.com/bible-quiz
   ```

## 🧪 Testing

The Docker setup includes comprehensive testing capabilities:

```bash
# Run all tests
make test

# Individual test types
docker-compose run --rm bible-quiz-test npm run build    # TypeScript compilation
docker-compose run --rm bible-quiz-test npm run lint     # Code linting
docker-compose run --rm bible-quiz-test npm run test     # Unit tests (when added)
```

## 🔍 Debugging

### View Container Logs
```bash
# Development logs
make dev-logs

# Production logs  
make prod-logs

# All container logs
docker logs <container-name>
```

### Access Container Shell
```bash
# Development container
docker-compose -f docker-compose.dev.yml exec bible-quiz-dev sh

# Production container
docker-compose -f docker-compose.prod.yml exec bible-quiz-prod sh
```

### Check Container Health
```bash
make health
# Or: docker-compose ps
```

## 🛠️ Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Stop existing containers
   make stop
   
   # Or kill process using port 3000
   lsof -ti:3000 | xargs kill -9
   ```

2. **Docker Build Failures**
   ```bash
   # Clean Docker cache
   make clean
   
   # Rebuild from scratch
   docker-compose build --no-cache
   ```

3. **Permission Issues (Linux/Mac)**
   ```bash
   # Make scripts executable
   chmod +x docker-scripts/*.sh
   ```

### Performance Optimization

1. **Use Docker Volumes for node_modules**
   - Already configured in `docker-compose.dev.yml`
   - Improves file watching performance

2. **Enable BuildKit**
   ```bash
   export DOCKER_BUILDKIT=1
   export COMPOSE_DOCKER_CLI_BUILD=1
   ```

## 📊 Monitoring

### Container Resource Usage
```bash
# View resource usage
docker stats

# View specific container
docker stats bible-quiz-dev
```

### Health Checks
```bash
# Check service health
curl -f http://localhost:3000 || echo "Service down"

# Docker health check
docker inspect --format='{{.State.Health.Status}}' <container-name>
```

## 🔐 Security

### Production Security Features

- **Non-root user**: Containers run as user `nextjs` (UID 1001)
- **Minimal base image**: Alpine Linux for smaller attack surface
- **Health checks**: Automatic service monitoring
- **Resource limits**: CPU and memory constraints in production

### Security Best Practices

1. **Keep images updated**
   ```bash
   docker pull node:18-alpine
   make build
   ```

2. **Scan for vulnerabilities**
   ```bash
   docker scan global-bible-quiz:latest
   ```

3. **Use secrets for sensitive data**
   - Configure in production deployment scripts
   - Never commit secrets to version control

## 📈 Scalability

### Horizontal Scaling
```bash
# Scale development environment
docker-compose -f docker-compose.dev.yml up --scale bible-quiz-dev=3

# Load balancer configuration needed for multiple instances
```

### Database Integration (Future)
```yaml
# Uncomment in docker-compose files for database support
postgres:
  image: postgres:15-alpine
  environment:
    POSTGRES_DB: bible_quiz
    POSTGRES_USER: quiz_user
    POSTGRES_PASSWORD: secure_password
```

## 🎯 Next Steps

1. **Set up CI/CD Pipeline**
   - GitHub Actions with Docker builds
   - Automated testing and deployment

2. **Add Monitoring**
   - Application metrics
   - Log aggregation
   - Performance monitoring

3. **Implement Caching**
   - Redis for quiz results
   - CDN for static assets

4. **Add SSL/TLS**
   - Let's Encrypt certificates
   - HTTPS configuration

---

## 📞 Support

For issues with Docker setup:

1. Check this documentation
2. Review container logs: `make logs`
3. Clean and rebuild: `make clean && make build`
4. Check Docker Desktop status and resources

Happy coding! 🚀