version: '3.8'

services:
  # Development service optimized for coding with auto-refresh
  bible-quiz-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload
      - .:/app
      # Use named volumes for node_modules and .next for better performance
      - node_modules:/app/node_modules
      - next_cache:/app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=true
    networks:
      - bible-quiz-network
    restart: unless-stopped
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Database service (if needed for future features like user accounts)
  # postgres:
  #   image: postgres:15-alpine
  #   ports:
  #     - "5432:5432"
  #   environment:
  #     - POSTGRES_DB=bible_quiz_dev
  #     - POSTGRES_USER=bible_quiz_user
  #     - POSTGRES_PASSWORD=dev_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - bible-quiz-network
  #   restart: unless-stopped

  # Redis for caching quiz results (future enhancement)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   networks:
  #     - bible-quiz-network
  #   restart: unless-stopped

networks:
  bible-quiz-network:
    driver: bridge

volumes:
  node_modules:
  next_cache:
  # postgres_data: