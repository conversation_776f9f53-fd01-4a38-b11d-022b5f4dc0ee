import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Global Bible Quiz - Master Scripture Knowledge | 1,189+ Bible Quizzes",
  description: "Test your Bible knowledge with comprehensive quizzes covering all 66 books, 200+ biblical characters, and 100+ themes. Strengthen your faith through interactive Scripture study.",
  keywords: [
    "Bible quiz",
    "Scripture study",
    "biblical knowledge",
    "Bible trivia",
    "Christian education",
    "Bible study",
    "biblical characters",
    "Old Testament",
    "New Testament",
    "faith building",
    "Genesis quiz",
    "Bible games",
    "Sunday school",
    "Christian learning"
  ],
  authors: [{ name: "Global Bible Quiz Team" }],
  creator: "Global Bible Quiz",
  publisher: "Global Bible Quiz",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://globalbiblequiz.com',
    title: 'Global Bible Quiz - Master Scripture Knowledge',
    description: 'Test your Bible knowledge with comprehensive quizzes covering all 66 books, 200+ biblical characters, and 100+ themes.',
    siteName: 'Global Bible Quiz',
    images: [
      {
        url: '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png',
        width: 1200,
        height: 630,
        alt: 'Global Bible Quiz - Interactive Scripture Learning',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Global Bible Quiz - Master Scripture Knowledge',
    description: 'Test your Bible knowledge with 1,189+ quizzes covering all 66 books and biblical characters.',
    images: ['/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png'],
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  category: 'Education',
  classification: 'Christian Education',
  other: {
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'Bible Quiz',
    'application-name': 'Global Bible Quiz',
    'msapplication-TileColor': '#2563eb',
    'theme-color': '#2563eb',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" type="image/png" href="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png" />
        <link rel="apple-touch-icon" href="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "EducationalOrganization",
              "name": "Global Bible Quiz",
              "description": "Comprehensive Bible quiz platform covering all 66 books, biblical characters, and themes",
              "url": "https://globalbiblequiz.com",
              "logo": "https://globalbiblequiz.com/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png",
              "sameAs": [
                "https://facebook.com/globalbiblequiz",
                "https://twitter.com/globalbiblequiz",
                "https://instagram.com/globalbiblequiz"
              ],
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD",
                "description": "Free Bible quizzes and study materials"
              },
              "educationalCredentialAwarded": "Bible Knowledge Certification",
              "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "name": "Bible Quiz Categories",
                "itemListElement": [
                  {
                    "@type": "Course",
                    "name": "Old Testament Quizzes",
                    "description": "Comprehensive quizzes covering all 39 Old Testament books"
                  },
                  {
                    "@type": "Course", 
                    "name": "New Testament Quizzes",
                    "description": "In-depth quizzes for all 27 New Testament books"
                  },
                  {
                    "@type": "Course",
                    "name": "Biblical Character Studies",
                    "description": "Character-focused quizzes for 200+ biblical figures"
                  },
                  {
                    "@type": "Course",
                    "name": "Biblical Theme Studies", 
                    "description": "Thematic quizzes covering 100+ biblical topics"
                  }
                ]
              }
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50`}
      >
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}