const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function comprehensiveBibleQuizTest() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  const page = await context.newPage();

  // Create screenshots directory
  const screenshotsDir = path.join(__dirname, 'test-screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir);
  }

  const testResults = {
    accessiblePort: null,
    consoleErrors: [],
    hydrationErrors: [],
    screenshots: [],
    functionalityTests: {},
    networkErrors: []
  };

  // Monitor console logs
  page.on('console', msg => {
    const text = msg.text();
    console.log(`CONSOLE [${msg.type()}]: ${text}`);
    
    if (msg.type() === 'error') {
      testResults.consoleErrors.push({
        type: msg.type(),
        text: text,
        timestamp: new Date().toISOString()
      });
    }
    
    // Check for hydration-specific errors
    if (text.includes('hydration') || text.includes('Hydration') || 
        text.includes('client-side') || text.includes('server-side')) {
      testResults.hydrationErrors.push({
        type: msg.type(),
        text: text,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Monitor network failures
  page.on('response', response => {
    if (!response.ok()) {
      testResults.networkErrors.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText()
      });
    }
  });

  console.log('🔍 Starting comprehensive Bible quiz testing...');

  // Test ports in sequence
  const ports = [3000, 3001, 8080, 8000, 5000];
  let accessibleUrl = null;

  for (const port of ports) {
    const url = `http://localhost:${port}`;
    console.log(`\n📡 Testing port ${port}...`);
    
    try {
      const response = await page.goto(url, { waitUntil: 'networkidle', timeout: 10000 });
      if (response && response.ok()) {
        console.log(`✅ Successfully accessed ${url}`);
        accessibleUrl = url;
        testResults.accessiblePort = port;
        break;
      }
    } catch (error) {
      console.log(`❌ Port ${port} not accessible: ${error.message}`);
    }
  }

  if (!accessibleUrl) {
    console.log('❌ No accessible ports found. Application may not be running.');
    await browser.close();
    return testResults;
  }

  console.log(`\n🎯 Using ${accessibleUrl} for testing`);

  // Wait for page to fully load
  await page.waitForTimeout(3000);

  // Capture main page screenshot
  const mainScreenshot = path.join(screenshotsDir, '01-main-page.png');
  await page.screenshot({ path: mainScreenshot, fullPage: true });
  testResults.screenshots.push('01-main-page.png');
  console.log('📸 Captured main page screenshot');

  // Test main page functionality
  console.log('\n🔧 Testing main page functionality...');
  
  try {
    // Check for page title
    const title = await page.title();
    console.log(`📋 Page title: ${title}`);
    testResults.functionalityTests.pageTitle = title;

    // Look for quiz-related elements
    const quizLinks = await page.$$eval('a[href*="quiz"], a[href*="chapter"]', 
      elements => elements.map(el => ({ href: el.href, text: el.textContent.trim() }))
    );
    
    console.log(`🔗 Found ${quizLinks.length} quiz-related links`);
    testResults.functionalityTests.quizLinks = quizLinks;

    // Test navigation to quiz pages
    if (quizLinks.length > 0) {
      console.log('\n🎮 Testing quiz page navigation...');
      
      for (let i = 0; i < Math.min(3, quizLinks.length); i++) {
        const link = quizLinks[i];
        console.log(`\n📍 Testing link: ${link.text} (${link.href})`);
        
        try {
          await page.goto(link.href, { waitUntil: 'networkidle', timeout: 15000 });
          await page.waitForTimeout(2000);
          
          // Capture screenshot of quiz page
          const quizScreenshot = path.join(screenshotsDir, `02-quiz-page-${i + 1}.png`);
          await page.screenshot({ path: quizScreenshot, fullPage: true });
          testResults.screenshots.push(`02-quiz-page-${i + 1}.png`);
          
          // Test ?start=true parameter bypass
          const bypassUrl = link.href + (link.href.includes('?') ? '&start=true' : '?start=true');
          console.log(`🚀 Testing bypass parameter: ${bypassUrl}`);
          
          await page.goto(bypassUrl, { waitUntil: 'networkidle', timeout: 15000 });
          await page.waitForTimeout(2000);
          
          const bypassScreenshot = path.join(screenshotsDir, `03-bypass-${i + 1}.png`);
          await page.screenshot({ path: bypassScreenshot, fullPage: true });
          testResults.screenshots.push(`03-bypass-${i + 1}.png`);
          
          // Test quiz functionality if quiz elements are present
          await testQuizFunctionality(page, testResults, screenshotsDir, i + 1);
          
        } catch (error) {
          console.log(`❌ Error testing quiz page ${i + 1}: ${error.message}`);
          testResults.functionalityTests[`quizPage${i + 1}Error`] = error.message;
        }
      }
    }

    // Test for specific quiz functionality on current page
    await testInteractiveElements(page, testResults, screenshotsDir);

  } catch (error) {
    console.log(`❌ Error during main page testing: ${error.message}`);
    testResults.functionalityTests.mainPageError = error.message;
  }

  // Final screenshot and cleanup
  const finalScreenshot = path.join(screenshotsDir, '99-final-state.png');
  await page.screenshot({ path: finalScreenshot, fullPage: true });
  testResults.screenshots.push('99-final-state.png');

  console.log('\n📊 Test Summary:');
  console.log(`✅ Accessible Port: ${testResults.accessiblePort}`);
  console.log(`⚠️  Console Errors: ${testResults.consoleErrors.length}`);
  console.log(`🔄 Hydration Errors: ${testResults.hydrationErrors.length}`);
  console.log(`🌐 Network Errors: ${testResults.networkErrors.length}`);
  console.log(`📸 Screenshots Taken: ${testResults.screenshots.length}`);

  await browser.close();
  return testResults;
}

async function testQuizFunctionality(page, testResults, screenshotsDir, pageIndex) {
  console.log(`\n🎯 Testing quiz functionality on page ${pageIndex}...`);
  
  try {
    // Look for quiz start button or quiz elements
    const startButton = await page.$('button[type="button"]:has-text("Start"), button:has-text("Begin"), .quiz-start, [data-testid*="start"]');
    
    if (startButton) {
      console.log('🎮 Found quiz start button, clicking...');
      await startButton.click();
      await page.waitForTimeout(2000);
      
      const quizStartedScreenshot = path.join(screenshotsDir, `04-quiz-started-${pageIndex}.png`);
      await page.screenshot({ path: quizStartedScreenshot, fullPage: true });
      testResults.screenshots.push(`04-quiz-started-${pageIndex}.png`);
    }

    // Look for quiz questions
    const questions = await page.$$('.question, [data-testid*="question"], .quiz-question');
    if (questions.length > 0) {
      console.log(`📝 Found ${questions.length} quiz questions`);
      testResults.functionalityTests[`quizQuestionsPage${pageIndex}`] = questions.length;
      
      // Test answering questions
      const answers = await page.$$('input[type="radio"], .answer-option, [data-testid*="answer"]');
      if (answers.length > 0) {
        console.log(`✅ Found ${answers.length} answer options`);
        
        // Select first answer
        await answers[0].click();
        await page.waitForTimeout(1000);
        
        const answeredScreenshot = path.join(screenshotsDir, `05-question-answered-${pageIndex}.png`);
        await page.screenshot({ path: answeredScreenshot, fullPage: true });
        testResults.screenshots.push(`05-question-answered-${pageIndex}.png`);
      }
    }

    // Check for timer
    const timer = await page.$('.timer, [data-testid*="timer"], .countdown');
    if (timer) {
      const timerText = await timer.textContent();
      console.log(`⏰ Timer found: ${timerText}`);
      testResults.functionalityTests[`timerPage${pageIndex}`] = timerText;
    }

    // Check for progress bar
    const progress = await page.$('.progress, [data-testid*="progress"], .quiz-progress');
    if (progress) {
      console.log('📊 Progress indicator found');
      testResults.functionalityTests[`progressPage${pageIndex}`] = true;
    }

  } catch (error) {
    console.log(`❌ Error testing quiz functionality: ${error.message}`);
    testResults.functionalityTests[`quizFunctionalityError${pageIndex}`] = error.message;
  }
}

async function testInteractiveElements(page, testResults, screenshotsDir) {
  console.log('\n🔧 Testing interactive elements...');
  
  try {
    // Test for buttons
    const buttons = await page.$$('button');
    console.log(`🔘 Found ${buttons.length} buttons`);
    
    // Test for forms
    const forms = await page.$$('form');
    console.log(`📋 Found ${forms.length} forms`);
    
    // Test for interactive quiz elements
    const interactiveElements = await page.$$('[data-testid], .interactive, .quiz, .question');
    console.log(`⚡ Found ${interactiveElements.length} interactive elements`);
    
    testResults.functionalityTests.interactiveElements = {
      buttons: buttons.length,
      forms: forms.length,
      interactive: interactiveElements.length
    };
    
  } catch (error) {
    console.log(`❌ Error testing interactive elements: ${error.message}`);
    testResults.functionalityTests.interactiveElementsError = error.message;
  }
}

// Run the test
comprehensiveBibleQuizTest().then(results => {
  console.log('\n📋 FINAL TEST RESULTS:');
  console.log(JSON.stringify(results, null, 2));
  
  // Write results to file
  fs.writeFileSync(
    path.join(__dirname, 'test-results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log('\n✅ Test completed! Results saved to test-results.json');
  console.log('📸 Screenshots saved in test-screenshots/ directory');
  
}).catch(error => {
  console.error('❌ Test failed:', error);
});