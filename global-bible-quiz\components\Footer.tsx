import Link from 'next/link';
import Image from 'next/image';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-gray-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="relative w-8 h-8">
                <Image
                  src="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png"
                  alt="Global Bible Quiz Logo"
                  width={32}
                  height={32}
                  className="w-8 h-8 object-cover rounded"
                />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Global Bible Quiz</h3>
                <p className="text-sm text-gray-400">Master Scripture Knowledge</p>
              </div>
            </div>
            <p className="text-sm text-gray-400 mb-4">
              Deepen your understanding of God's Word through interactive quizzes covering all 66 books of the Bible, 
              200+ characters, and 100+ themes.
            </p>
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <span>📊 1,189+ Quizzes</span>
              <span>•</span>
              <span>📖 All 66 Books</span>
            </div>
          </div>

          {/* Bible Books */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Bible Books</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/genesis-quiz" className="hover:text-white transition-colors">Genesis</Link></li>
              <li><Link href="/exodus-quiz" className="hover:text-white transition-colors">Exodus</Link></li>
              <li><Link href="/leviticus-quiz" className="hover:text-white transition-colors">Leviticus</Link></li>
              <li><Link href="/matthew-quiz" className="hover:text-white transition-colors">Matthew</Link></li>
              <li><Link href="/mark-quiz" className="hover:text-white transition-colors">Mark</Link></li>
              <li><Link href="/john-quiz" className="hover:text-white transition-colors">John</Link></li>
              <li><Link href="/bible-quizzes" className="text-blue-400 hover:text-blue-300 transition-colors">View All Books →</Link></li>
            </ul>
          </div>

          {/* Characters & Themes */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Study Topics</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/abraham-quiz" className="hover:text-white transition-colors">Abraham</Link></li>
              <li><Link href="/moses-quiz" className="hover:text-white transition-colors">Moses</Link></li>
              <li><Link href="/david-quiz" className="hover:text-white transition-colors">David</Link></li>
              <li><Link href="/jesus-quiz" className="hover:text-white transition-colors">Jesus Christ</Link></li>
              <li><Link href="/paul-quiz" className="hover:text-white transition-colors">Paul</Link></li>
              <li><Link href="/salvation-quiz" className="hover:text-white transition-colors">Salvation</Link></li>
              <li><Link href="/characters" className="text-blue-400 hover:text-blue-300 transition-colors">All Characters →</Link></li>
            </ul>
          </div>

          {/* Resources & Info */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Resources</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/study-guides" className="hover:text-white transition-colors">Study Guides</Link></li>
              <li><Link href="/quiz-tips" className="hover:text-white transition-colors">Quiz Tips</Link></li>
              <li><Link href="/bible-reading-plans" className="hover:text-white transition-colors">Reading Plans</Link></li>
              <li><Link href="/about" className="hover:text-white transition-colors">About Us</Link></li>
              <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
            </ul>
          </div>
        </div>

        {/* Quick Quiz Section */}
        <div className="border-t border-gray-800 pt-8 mt-8">
          <div className="bg-blue-600 rounded-lg p-6 text-white text-center">
            <h4 className="text-lg font-semibold mb-2">Ready to Test Your Bible Knowledge?</h4>
            <p className="text-blue-100 mb-4">Take a random quiz and discover new insights from Scripture</p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/random-quiz" className="bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                🎲 Random Quiz
              </Link>
              <Link href="/genesis-quiz" className="bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-800 transition-colors">
                📖 Start with Genesis
              </Link>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 pt-8 mt-8 text-center">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-400">
              © 2024 Global Bible Quiz. Designed for the glory of God and the growth of His people.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <div className="text-sm text-gray-400">
                "Study to show yourself approved unto God" - 2 Timothy 2:15
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}