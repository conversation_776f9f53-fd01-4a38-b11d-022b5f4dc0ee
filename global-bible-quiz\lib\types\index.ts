// Core TypeScript interfaces for the comprehensive Bible quiz platform

export interface BibleBook {
  id: string;
  name: string;
  slug: string;
  testament: 'old' | 'new';
  chapters: number;
  description: string;
  keyThemes: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // in minutes
}

export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'matching';
  options?: string[]; // for multiple choice
  correctAnswer: string | number;
  explanation: string;
  verseReference: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
}

export interface Quiz {
  id: string;
  title: string;
  slug: string;
  description: string;
  type: 'chapter' | 'book' | 'character' | 'theme' | 'difficulty';
  category: string;
  subcategory?: string;
  questions: QuizQuestion[];
  isBookQuiz: boolean;
  bookId?: string;
  chapterNumber?: number;
  characterId?: string;
  themeId?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  tags: string[];
  seoData: SEOData;
}

export interface BiblicalCharacter {
  id: string;
  name: string;
  slug: string;
  description: string;
  testament: 'old' | 'new' | 'both';
  keyVerses: string[];
  associatedBooks: string[];
  significance: string;
  timelinePosition: number;
}

export interface ThemeCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  subcategories: string[];
  associatedBooks: string[];
  keyVerses: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  ogImage?: string;
  jsonLd: any; // Schema.org structured data
}

export interface QuizResult {
  quizId: string;
  score: number;
  totalQuestions: number;
  percentage: number;
  timeSpent: number;
  answeredQuestions: {
    questionId: string;
    userAnswer: string | number;
    isCorrect: boolean;
    timeSpent: number;
  }[];
  completedAt: Date;
  userId?: string;
}

export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  isActive?: boolean;
}

// Hub page types for comprehensive content organization
export interface QuizHub {
  id: string;
  title: string;
  slug: string;
  description: string;
  featuredQuizzes: Quiz[];
  categories: {
    name: string;
    quizzes: Quiz[];
  }[];
  seoData: SEOData;
}

// Content management types
export interface ContentMeta {
  totalBooks: number;
  totalChapters: number;
  totalQuizzes: number;
  totalQuestions: number;
  lastUpdated: Date;
  featuredContent: {
    books: string[];
    characters: string[];
    themes: string[];
  };
}

// Quiz generation types for dynamic content
export interface QuizTemplate {
  type: Quiz['type'];
  questionCount: number;
  difficultyDistribution: {
    easy: number;
    medium: number;
    hard: number;
  };
  questionTypes: {
    'multiple-choice': number;
    'true-false': number;
    'fill-blank': number;
    'matching': number;
  };
}

// User progress tracking
export interface UserProgress {
  userId: string;
  completedQuizzes: string[];
  averageScore: number;
  totalTime: number;
  achievements: string[];
  currentStreak: number;
  favoriteTopics: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}