<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Bible Quiz - Mobile Responsiveness Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Visual testing aids */
        .test-grid {
            background-image: 
                linear-gradient(rgba(0,0,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .viewport-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 9999;
        }
        
        .constraint-70 {
            max-width: 70vw;
            border: 2px dashed #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .measurement-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 9998;
        }
        
        .measurement-line {
            position: absolute;
            background: #10b981;
            opacity: 0.7;
        }
        
        .measurement-line.vertical {
            width: 2px;
            height: 100vh;
        }
        
        .measurement-line.horizontal {
            height: 2px;
            width: 100vw;
        }
    </style>
</head>
<body class="bg-gray-50 test-grid">
    <!-- Viewport Indicator -->
    <div id="viewport-indicator" class="viewport-indicator">
        Screen: <span id="screen-size"></span> | 70% = <span id="seventy-percent"></span>px
    </div>

    <!-- Measurement Overlay -->
    <div class="measurement-overlay">
        <div class="measurement-line vertical" style="left: 70%"></div>
        <div class="measurement-line horizontal" style="top: 10%"></div>
    </div>

    <!-- Test Header (Simulating our Header component) -->
    <header class="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-600 rounded"></div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Global Bible Quiz</h1>
                        <p class="text-xs text-gray-600 hidden sm:block">Master Scripture Knowledge</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Home</a>
                    <div class="relative">
                        <button class="flex items-center text-gray-700 hover:text-blue-600 font-medium">
                            Bible Quizzes
                            <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Study Guides</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Characters</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Resources</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">About</a>
                    <a href="#" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Login</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="lg:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu - CRITICAL: 70% Width Constraint Test -->
            <div id="mobile-menu" class="lg:hidden absolute top-16 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40 hidden">
                <div class="constraint-70 mx-auto px-4 py-4">
                    <div class="bg-yellow-100 border border-yellow-300 rounded p-3 mb-4 text-sm">
                        🧪 <strong>TEST MODE:</strong> This mobile menu is constrained to 70% of viewport width as required.
                        <br>Red dashed border shows the constraint boundary.
                    </div>
                    
                    <nav class="space-y-4">
                        <a href="#" class="block text-gray-700 hover:text-blue-600 font-medium py-2">🏠 Home</a>
                        
                        <div class="space-y-2">
                            <div class="text-gray-900 font-semibold">📚 Bible Quizzes</div>
                            <div class="ml-4 space-y-2">
                                <a href="#" class="block text-sm text-gray-600 hover:text-blue-600 py-1">All Bible Books</a>
                                <a href="#" class="block text-sm text-gray-600 hover:text-blue-600 py-1">Genesis Quiz</a>
                                <a href="#" class="block text-sm text-gray-600 hover:text-blue-600 py-1">Exodus Quiz</a>
                            </div>
                        </div>

                        <a href="#" class="block text-gray-700 hover:text-blue-600 font-medium py-2">📖 Study Guides</a>
                        <a href="#" class="block text-gray-700 hover:text-blue-600 font-medium py-2">👥 Characters</a>
                        <a href="#" class="block text-gray-700 hover:text-blue-600 font-medium py-2">📋 Resources</a>
                        <a href="#" class="block text-gray-700 hover:text-blue-600 font-medium py-2">ℹ️ About</a>
                        <a href="#" class="block bg-blue-600 text-white px-4 py-2 rounded-lg text-center font-medium hover:bg-blue-700">🔐 Login</a>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">📱 Mobile Responsiveness Test</h1>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-blue-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-blue-900 mb-4">✅ Test Results</h2>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center gap-2">
                            <span class="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></span>
                            70% mobile navigation constraint implemented
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></span>
                            Responsive breakpoints (sm, md, lg, xl)
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></span>
                            Mobile-first design approach
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></span>
                            Touch-friendly interactive elements
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></span>
                            Proper viewport meta tag
                        </li>
                    </ul>
                </div>

                <div class="bg-yellow-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-yellow-900 mb-4">🔍 Testing Instructions</h2>
                    <ol class="space-y-2 text-sm list-decimal list-inside">
                        <li>Resize your browser window to different widths</li>
                        <li>Click the mobile menu button (☰) in the top right</li>
                        <li>Verify the mobile menu stays within the red dashed boundary</li>
                        <li>Check that content is readable at all screen sizes</li>
                        <li>Test on actual mobile devices if possible</li>
                    </ol>

                    <div class="mt-4 p-3 bg-white rounded border">
                        <p class="text-xs text-gray-600">
                            <strong>Green line:</strong> 70% viewport width marker<br>
                            <strong>Red dashed border:</strong> Mobile menu boundary<br>
                            <strong>Grid background:</strong> Alignment reference
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Linear Quiz Format Test -->
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 Linear Quiz Format Test</h2>
            
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                <p class="text-gray-700 mb-4">
                    Answer all questions below and click "Submit Quiz and See Results" when you're finished.
                    This demonstrates the linear format where all questions are displayed on a single scrollable page.
                </p>
                <div class="flex items-center justify-center gap-6 text-sm text-gray-600">
                    <span>📊 5 Questions</span>
                    <span>⏱️ ~2 Minutes</span>
                    <span>📖 Test Format</span>
                </div>
            </div>

            <!-- Sample Questions in Linear Format -->
            <div class="space-y-6 mb-8">
                <h3 class="text-xl font-bold text-gray-900">Quiz Questions</h3>
                
                <!-- Question 1 -->
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <div class="mb-4">
                        <div class="flex items-center gap-2 mb-3">
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">easy</span>
                            <span class="text-sm text-gray-500">Test Question 1</span>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900">
                            1. What is the main purpose of this mobile responsiveness test?
                        </h4>
                    </div>
                    <div class="space-y-2">
                        <label class="flex items-center p-3 rounded-lg border-2 border-gray-200 hover:border-gray-300 cursor-pointer">
                            <input type="radio" name="q1" value="0" class="mr-3">
                            <span class="text-sm font-medium">A. To test colors</span>
                        </label>
                        <label class="flex items-center p-3 rounded-lg border-2 border-gray-200 hover:border-gray-300 cursor-pointer">
                            <input type="radio" name="q1" value="1" class="mr-3">
                            <span class="text-sm font-medium">B. To verify 70% mobile navigation constraint</span>
                        </label>
                        <label class="flex items-center p-3 rounded-lg border-2 border-gray-200 hover:border-gray-300 cursor-pointer">
                            <input type="radio" name="q1" value="2" class="mr-3">
                            <span class="text-sm font-medium">C. To test fonts</span>
                        </label>
                    </div>
                </div>

                <!-- Question 2 -->
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <div class="mb-4">
                        <div class="flex items-center gap-2 mb-3">
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">medium</span>
                            <span class="text-sm text-gray-500">Test Question 2</span>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900">
                            2. Which CSS property limits the mobile menu width?
                        </h4>
                    </div>
                    <div class="space-y-2">
                        <label class="flex items-center p-3 rounded-lg border-2 border-gray-200 hover:border-gray-300 cursor-pointer">
                            <input type="radio" name="q2" value="0" class="mr-3">
                            <span class="text-sm font-medium">A. max-width: 70vw</span>
                        </label>
                        <label class="flex items-center p-3 rounded-lg border-2 border-gray-200 hover:border-gray-300 cursor-pointer">
                            <input type="radio" name="q2" value="1" class="mr-3">
                            <span class="text-sm font-medium">B. width: 100%</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Single Submit Button -->
            <div class="text-center mb-8">
                <button class="px-8 py-4 bg-green-600 text-white text-lg font-semibold rounded-lg hover:bg-green-700 transition-colors shadow-lg">
                    Submit Quiz and See Results
                </button>
                <p class="text-sm text-gray-600 mt-2">
                    Make sure you've answered all questions before submitting
                </p>
            </div>

            <!-- Internal Links Section (Demo) -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">Continue Your Bible Study Journey</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        📖 Study Guide
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        → Next Quiz
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors">
                        👤 Character Study
                    </a>
                </div>
            </div>
        </div>

        <!-- Breakpoint Testing -->
        <div class="bg-white rounded-xl shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📐 Responsive Breakpoint Testing</h2>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                <div class="bg-red-100 p-4 rounded-lg text-center">
                    <div class="text-red-800 font-semibold">XS</div>
                    <div class="text-xs text-red-600">< 640px</div>
                    <div class="text-xs text-red-600 block sm:hidden">✅ Active</div>
                </div>
                <div class="bg-orange-100 p-4 rounded-lg text-center">
                    <div class="text-orange-800 font-semibold">SM</div>
                    <div class="text-xs text-orange-600">≥ 640px</div>
                    <div class="text-xs text-orange-600 hidden sm:block md:hidden">✅ Active</div>
                </div>
                <div class="bg-yellow-100 p-4 rounded-lg text-center">
                    <div class="text-yellow-800 font-semibold">MD</div>
                    <div class="text-xs text-yellow-600">≥ 768px</div>
                    <div class="text-xs text-yellow-600 hidden md:block lg:hidden">✅ Active</div>
                </div>
                <div class="bg-green-100 p-4 rounded-lg text-center">
                    <div class="text-green-800 font-semibold">LG</div>
                    <div class="text-xs text-green-600">≥ 1024px</div>
                    <div class="text-xs text-green-600 hidden lg:block xl:hidden">✅ Active</div>
                </div>
                <div class="bg-blue-100 p-4 rounded-lg text-center">
                    <div class="text-blue-800 font-semibold">XL</div>
                    <div class="text-xs text-blue-600">≥ 1280px</div>
                    <div class="text-xs text-blue-600 hidden xl:block">✅ Active</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Update viewport indicator
        function updateViewport() {
            const width = window.innerWidth;
            const seventyPercent = Math.floor(width * 0.7);
            
            document.getElementById('screen-size').textContent = width + 'px';
            document.getElementById('seventy-percent').textContent = seventyPercent;
        }

        // Update on load and resize
        updateViewport();
        window.addEventListener('resize', updateViewport);

        // Quiz form handling
        document.querySelector('button[type="button"]').addEventListener('click', function() {
            alert('✅ Quiz submitted successfully!\n\nThis demonstrates the linear quiz format where:\n• All questions are on one page\n• Single submit button\n• Internal links follow\n\n🎉 Mobile responsiveness test complete!');
        });

        // Highlight selected answers
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove highlight from siblings
                const name = this.name;
                document.querySelectorAll(`input[name="${name}"]`).forEach(r => {
                    r.closest('label').classList.remove('border-blue-500', 'bg-blue-50');
                });
                
                // Highlight selected
                this.closest('label').classList.add('border-blue-500', 'bg-blue-50');
            });
        });
    </script>
</body>
</html>