import { <PERSON><PERSON><PERSON><PERSON> } from '@/lib/types';

// Comprehensive data for 200+ biblical characters
export const BIBLICAL_CHARACTERS: Biblical<PERSON>haracter[] = [
  // MAJOR OLD TESTAMENT CHARACTERS
  {
    id: 'abraham',
    name: '<PERSON>',
    slug: 'abraham',
    description: 'The father of faith, called by <PERSON> to leave his homeland and become the father of many nations',
    testament: 'old',
    keyVerses: ['Genesis 12:1-3', 'Genesis 15:6', 'Genesis 22:1-18', 'Romans 4:16-17'],
    associatedBooks: ['Genesis', 'Romans', 'Hebrews', '<PERSON>'],
    significance: 'Father of the Jewish nation and model of faith',
    timelinePosition: 1
  },
  {
    id: 'moses',
    name: '<PERSON>',
    slug: 'moses',
    description: 'The great lawgiver who led Israel out of Egypt and received the Ten Commandments',
    testament: 'old',
    keyVerses: ['Exodus 3:1-15', 'Exodus 20:1-17', 'Deuteronomy 34:10-12'],
    associatedBooks: ['Exodus', 'Levi<PERSON>', 'Numbers', 'Deuteronomy'],
    significance: 'Deliverer of Israel and mediator of the Old Covenant',
    timelinePosition: 2
  },
  {
    id: 'david',
    name: '<PERSON>',
    slug: 'david',
    description: 'The shepherd boy who became king, a man after God\'s own heart',
    testament: 'old',
    keyVerses: ['1 <PERSON> 16:7', '1 <PERSON> 17:45-47', '2 <PERSON> 7:12-16', 'Psalm 23'],
    associatedBooks: ['1 <PERSON>', '2 <PERSON>', '1 Kings', 'Psalms'],
    significance: 'Greatest king of <PERSON> and ancestor of <PERSON>',
    timelinePosition: 5
  },
  {
    id: 'solomon',
    name: '<PERSON>',
    slug: 'solomon',
    description: 'The wisest king who built the temple but fell into idolatry',
    testament: 'old',
    keyVerses: ['1 Kings 3:9-12', '1 Kings 6:1', '1 Kings 11:4-9'],
    associatedBooks: ['1 Kings', '2 Chronicles', 'Proverbs', 'Ecclesiastes'],
    significance: 'Wisest man and builder of the first temple',
    timelinePosition: 6
  },
  {
    id: 'noah',
    name: 'Noah',
    slug: 'noah',
    description: 'The righteous man who built the ark and survived the flood',
    testament: 'old',
    keyVerses: ['Genesis 6:9', 'Genesis 7:1', 'Genesis 9:8-17'],
    associatedBooks: ['Genesis', '2 Peter', 'Hebrews'],
    significance: 'Preserver of humanity through the flood',
    timelinePosition: 0
  },
  {
    id: 'joseph',
    name: 'Joseph',
    slug: 'joseph',
    description: 'The dreamer who became second in command in Egypt',
    testament: 'old',
    keyVerses: ['Genesis 37:5-11', 'Genesis 41:39-41', 'Genesis 50:20'],
    associatedBooks: ['Genesis'],
    significance: 'Savior of his family during famine',
    timelinePosition: 1
  },
  {
    id: 'daniel',
    name: 'Daniel',
    slug: 'daniel',
    description: 'The prophet who remained faithful in Babylonian exile',
    testament: 'old',
    keyVerses: ['Daniel 1:8', 'Daniel 6:10', 'Daniel 9:23'],
    associatedBooks: ['Daniel', 'Ezekiel'],
    significance: 'Model of faithfulness in exile',
    timelinePosition: 8
  },
  {
    id: 'elijah',
    name: 'Elijah',
    slug: 'elijah',
    description: 'The fiery prophet who confronted Ahab and defeated the prophets of Baal',
    testament: 'old',
    keyVerses: ['1 Kings 17:1', '1 Kings 18:36-39', '2 Kings 2:11'],
    associatedBooks: ['1 Kings', '2 Kings', 'Malachi'],
    significance: 'Great prophet who did not die but was taken up',
    timelinePosition: 7
  },
  {
    id: 'joshua',
    name: 'Joshua',
    slug: 'joshua',
    description: 'Moses\' successor who led Israel into the Promised Land',
    testament: 'old',
    keyVerses: ['Joshua 1:6-9', 'Joshua 6:20', 'Joshua 24:15'],
    associatedBooks: ['Numbers', 'Deuteronomy', 'Joshua'],
    significance: 'Conqueror of the Promised Land',
    timelinePosition: 3
  },
  {
    id: 'samuel',
    name: 'Samuel',
    slug: 'samuel',
    description: 'The last judge and first prophet who anointed Israel\'s first kings',
    testament: 'old',
    keyVerses: ['1 Samuel 3:19', '1 Samuel 8:7', '1 Samuel 16:13'],
    associatedBooks: ['1 Samuel'],
    significance: 'Transition figure from judges to monarchy',
    timelinePosition: 4
  },

  // MAJOR NEW TESTAMENT CHARACTERS
  {
    id: 'jesus',
    name: 'Jesus Christ',
    slug: 'jesus',
    description: 'The Son of God, Savior of the world, and central figure of Christianity',
    testament: 'new',
    keyVerses: ['John 1:1', 'John 3:16', 'Matthew 28:18-20', 'Philippians 2:5-11'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', 'All NT books'],
    significance: 'Savior and Lord, central figure of all Scripture',
    timelinePosition: 10
  },
  {
    id: 'paul',
    name: 'Paul (Saul)',
    slug: 'paul',
    description: 'The apostle to the Gentiles, greatest missionary and theologian',
    testament: 'new',
    keyVerses: ['Acts 9:3-6', 'Romans 1:16', 'Philippians 3:7-8', '2 Timothy 4:7-8'],
    associatedBooks: ['Acts', 'Romans', '1 Corinthians', '2 Corinthians', 'Galatians', 'Ephesians', 'Philippians', 'Colossians', '1 Thessalonians', '2 Thessalonians', '1 Timothy', '2 Timothy', 'Titus', 'Philemon'],
    significance: 'Apostle to the Gentiles and greatest missionary',
    timelinePosition: 11
  },
  {
    id: 'peter',
    name: 'Peter (Simon)',
    slug: 'peter',
    description: 'The fisherman who became the rock of the early church',
    testament: 'new',
    keyVerses: ['Matthew 16:16', 'Luke 22:31-32', 'John 21:15-17', 'Acts 2:14'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', '1 Peter', '2 Peter'],
    significance: 'Leader of the apostles and pillar of the early church',
    timelinePosition: 10
  },
  {
    id: 'john-apostle',
    name: 'John the Apostle',
    slug: 'john-apostle',
    description: 'The beloved disciple, author of the Gospel of John and Revelation',
    testament: 'new',
    keyVerses: ['John 13:23', 'John 19:26-27', '1 John 4:7-8', 'Revelation 1:9'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', '1 John', '2 John', '3 John', 'Revelation'],
    significance: 'Beloved disciple and author of love',
    timelinePosition: 10
  },
  {
    id: 'mary-mother',
    name: 'Mary (Mother of Jesus)',
    slug: 'mary-mother',
    description: 'The virgin who gave birth to Jesus, blessed among women',
    testament: 'new',
    keyVerses: ['Luke 1:26-38', 'Luke 1:46-55', 'Luke 2:19', 'John 19:25-27'],
    associatedBooks: ['Matthew', 'Luke', 'John', 'Acts'],
    significance: 'Mother of Jesus and model of faith',
    timelinePosition: 10
  },
  {
    id: 'john-baptist',
    name: 'John the Baptist',
    slug: 'john-baptist',
    description: 'The forerunner who prepared the way for Jesus',
    testament: 'new',
    keyVerses: ['Matthew 3:1-3', 'John 1:29', 'Matthew 11:11', 'Luke 1:15'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    significance: 'Forerunner and baptizer of Jesus',
    timelinePosition: 10
  },

  // WOMEN OF THE BIBLE
  {
    id: 'sarah',
    name: 'Sarah',
    slug: 'sarah',
    description: 'Abraham\'s wife who laughed at God\'s promise but bore Isaac in old age',
    testament: 'old',
    keyVerses: ['Genesis 18:12', 'Genesis 21:1-7', 'Romans 4:19', 'Hebrews 11:11'],
    associatedBooks: ['Genesis', 'Romans', 'Hebrews'],
    significance: 'Mother of Isaac and example of faith',
    timelinePosition: 1
  },
  {
    id: 'rebekah',
    name: 'Rebekah',
    slug: 'rebekah',
    description: 'Isaac\'s wife who showed kindness at the well',
    testament: 'old',
    keyVerses: ['Genesis 24:18-20', 'Genesis 25:23', 'Genesis 27:5-17'],
    associatedBooks: ['Genesis'],
    significance: 'Mother of Jacob and Esau',
    timelinePosition: 1
  },
  {
    id: 'rachel',
    name: 'Rachel',
    slug: 'rachel',
    description: 'Jacob\'s beloved wife, mother of Joseph and Benjamin',
    testament: 'old',
    keyVerses: ['Genesis 29:17', 'Genesis 30:22-24', 'Genesis 35:16-18'],
    associatedBooks: ['Genesis'],
    significance: 'Beloved wife of Jacob and mother of Joseph',
    timelinePosition: 1
  },
  {
    id: 'ruth',
    name: 'Ruth',
    slug: 'ruth',
    description: 'The Moabite woman who showed loyalty to Naomi',
    testament: 'old',
    keyVerses: ['Ruth 1:16-17', 'Ruth 2:11-12', 'Ruth 4:13-17'],
    associatedBooks: ['Ruth', 'Matthew'],
    significance: 'Model of loyalty and ancestor of David',
    timelinePosition: 4
  },
  {
    id: 'esther',
    name: 'Esther',
    slug: 'esther',
    description: 'The Jewish queen who saved her people from destruction',
    testament: 'old',
    keyVerses: ['Esther 4:14', 'Esther 7:3-4', 'Esther 9:1'],
    associatedBooks: ['Esther'],
    significance: 'Deliverer of the Jewish people',
    timelinePosition: 8
  },
  {
    id: 'deborah',
    name: 'Deborah',
    slug: 'deborah',
    description: 'The prophetess and judge who led Israel to victory',
    testament: 'old',
    keyVerses: ['Judges 4:4-5', 'Judges 4:14', 'Judges 5:1-31'],
    associatedBooks: ['Judges'],
    significance: 'Only female judge and military leader',
    timelinePosition: 3
  },
  {
    id: 'mary-magdalene',
    name: 'Mary Magdalene',
    slug: 'mary-magdalene',
    description: 'The devoted follower who was first to see the risen Christ',
    testament: 'new',
    keyVerses: ['Luke 8:2', 'John 19:25', 'John 20:11-18', 'Mark 16:9'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    significance: 'First witness of the resurrection',
    timelinePosition: 10
  },
  {
    id: 'martha',
    name: 'Martha',
    slug: 'martha',
    description: 'The sister of Mary and Lazarus who served Jesus',
    testament: 'new',
    keyVerses: ['Luke 10:38-42', 'John 11:20-27', 'John 12:2'],
    associatedBooks: ['Luke', 'John'],
    significance: 'Example of service and faith',
    timelinePosition: 10
  },

  // KINGS AND RULERS
  {
    id: 'saul',
    name: 'Saul',
    slug: 'saul',
    description: 'Israel\'s first king who started well but ended in tragedy',
    testament: 'old',
    keyVerses: ['1 Samuel 9:2', '1 Samuel 10:9-10', '1 Samuel 15:22-23', '1 Samuel 31:4'],
    associatedBooks: ['1 Samuel'],
    significance: 'First king of Israel',
    timelinePosition: 4
  },
  {
    id: 'hezekiah',
    name: 'Hezekiah',
    slug: 'hezekiah',
    description: 'The good king who trusted in the Lord during Assyrian invasion',
    testament: 'old',
    keyVerses: ['2 Kings 18:5-6', '2 Kings 19:14-19', '2 Kings 20:1-6'],
    associatedBooks: ['2 Kings', '2 Chronicles', 'Isaiah'],
    significance: 'One of Judah\'s greatest kings',
    timelinePosition: 7
  },
  {
    id: 'josiah',
    name: 'Josiah',
    slug: 'josiah',
    description: 'The reformer king who rediscovered the Book of the Law',
    testament: 'old',
    keyVerses: ['2 Kings 22:2', '2 Kings 23:25', '2 Chronicles 34:2'],
    associatedBooks: ['2 Kings', '2 Chronicles'],
    significance: 'Great reformer king of Judah',
    timelinePosition: 8
  },
  {
    id: 'nebuchadnezzar',
    name: 'Nebuchadnezzar',
    slug: 'nebuchadnezzar',
    description: 'The Babylonian king who conquered Jerusalem but came to know God',
    testament: 'old',
    keyVerses: ['Daniel 2:47', 'Daniel 3:28-29', 'Daniel 4:34-37'],
    associatedBooks: ['2 Kings', 'Daniel', 'Jeremiah'],
    significance: 'Babylonian king who acknowledged God',
    timelinePosition: 8
  },

  // PROPHETS
  {
    id: 'isaiah',
    name: 'Isaiah',
    slug: 'isaiah',
    description: 'The messianic prophet who saw the Lord\'s glory',
    testament: 'old',
    keyVerses: ['Isaiah 6:1-8', 'Isaiah 9:6', 'Isaiah 53:4-6', 'Isaiah 55:11'],
    associatedBooks: ['Isaiah', '2 Kings'],
    significance: 'Great messianic prophet',
    timelinePosition: 7
  },
  {
    id: 'jeremiah',
    name: 'Jeremiah',
    slug: 'jeremiah',
    description: 'The weeping prophet who warned of Jerusalem\'s destruction',
    testament: 'old',
    keyVerses: ['Jeremiah 1:4-10', 'Jeremiah 9:1', 'Jeremiah 31:31-34'],
    associatedBooks: ['Jeremiah', 'Lamentations'],
    significance: 'Prophet of the new covenant',
    timelinePosition: 8
  },
  {
    id: 'ezekiel',
    name: 'Ezekiel',
    slug: 'ezekiel',
    description: 'The prophet of visions who ministered during the exile',
    testament: 'old',
    keyVerses: ['Ezekiel 1:1', 'Ezekiel 36:26', 'Ezekiel 37:1-14'],
    associatedBooks: ['Ezekiel'],
    significance: 'Prophet of restoration and new heart',
    timelinePosition: 8
  },

  // DISCIPLES AND APOSTLES
  {
    id: 'matthew',
    name: 'Matthew (Levi)',
    slug: 'matthew',
    description: 'The tax collector who became an apostle and Gospel writer',
    testament: 'new',
    keyVerses: ['Matthew 9:9', 'Matthew 10:3'],
    associatedBooks: ['Matthew', 'Mark', 'Luke'],
    significance: 'Apostle and Gospel writer',
    timelinePosition: 10
  },
  {
    id: 'james-apostle',
    name: 'James (son of Zebedee)',
    slug: 'james-apostle',
    description: 'One of the inner circle, first apostle to be martyred',
    testament: 'new',
    keyVerses: ['Matthew 4:21', 'Acts 12:2'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'Acts'],
    significance: 'First apostle to be martyred',
    timelinePosition: 10
  },
  {
    id: 'thomas',
    name: 'Thomas (Doubting Thomas)',
    slug: 'thomas',
    description: 'The apostle who doubted but then declared Jesus as Lord and God',
    testament: 'new',
    keyVerses: ['John 11:16', 'John 20:24-29'],
    associatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    significance: 'Apostle who overcame doubt with faith',
    timelinePosition: 10
  },
  {
    id: 'barnabas',
    name: 'Barnabas',
    slug: 'barnabas',
    description: 'The encourager who mentored Paul and John Mark',
    testament: 'new',
    keyVerses: ['Acts 4:36', 'Acts 9:27', 'Acts 11:24'],
    associatedBooks: ['Acts'],
    significance: 'Son of encouragement and missionary',
    timelinePosition: 11
  },
  {
    id: 'timothy',
    name: 'Timothy',
    slug: 'timothy',
    description: 'Paul\'s spiritual son and faithful companion',
    testament: 'new',
    keyVerses: ['Acts 16:1-3', '1 Timothy 4:12', '2 Timothy 1:5'],
    associatedBooks: ['Acts', '1 Timothy', '2 Timothy'],
    significance: 'Paul\'s trusted disciple and church leader',
    timelinePosition: 11
  },

  // ADDITIONAL SIGNIFICANT CHARACTERS
  {
    id: 'job',
    name: 'Job',
    slug: 'job',
    description: 'The righteous man who suffered greatly but remained faithful',
    testament: 'old',
    keyVerses: ['Job 1:1', 'Job 13:15', 'Job 19:25-26', 'Job 42:10'],
    associatedBooks: ['Job'],
    significance: 'Example of faith through suffering',
    timelinePosition: 1
  },
  {
    id: 'jonah',
    name: 'Jonah',
    slug: 'jonah',
    description: 'The reluctant prophet who was swallowed by a great fish',
    testament: 'old',
    keyVerses: ['Jonah 1:1-3', 'Jonah 1:17', 'Jonah 3:1-3', 'Matthew 12:40'],
    associatedBooks: ['Jonah', 'Matthew'],
    significance: 'Prophet to Nineveh and sign of resurrection',
    timelinePosition: 7
  },
  {
    id: 'gideon',
    name: 'Gideon',
    slug: 'gideon',
    description: 'The judge who defeated the Midianites with 300 men',
    testament: 'old',
    keyVerses: ['Judges 6:12', 'Judges 7:7', 'Judges 8:23'],
    associatedBooks: ['Judges'],
    significance: 'Judge who trusted God despite fear',
    timelinePosition: 3
  },
  {
    id: 'samson',
    name: 'Samson',
    slug: 'samson',
    description: 'The strong judge whose weakness was his downfall',
    testament: 'old',
    keyVerses: ['Judges 13:5', 'Judges 16:17', 'Judges 16:28-30'],
    associatedBooks: ['Judges'],
    significance: 'Strongest judge with greatest weakness',
    timelinePosition: 3
  },
  {
    id: 'elisha',
    name: 'Elisha',
    slug: 'elisha',
    description: 'Elijah\'s successor who performed many miracles',
    testament: 'old',
    keyVerses: ['1 Kings 19:16', '2 Kings 2:9', '2 Kings 4:32-35'],
    associatedBooks: ['1 Kings', '2 Kings'],
    significance: 'Prophet of miracles and healing',
    timelinePosition: 7
  },
  {
    id: 'nehemiah',
    name: 'Nehemiah',
    slug: 'nehemiah',
    description: 'The cupbearer who rebuilt Jerusalem\'s walls',
    testament: 'old',
    keyVerses: ['Nehemiah 1:4', 'Nehemiah 2:17-18', 'Nehemiah 6:15'],
    associatedBooks: ['Nehemiah'],
    significance: 'Rebuilder of Jerusalem\'s walls',
    timelinePosition: 8
  },
  {
    id: 'ezra',
    name: 'Ezra',
    slug: 'ezra',
    description: 'The scribe who led spiritual revival',
    testament: 'old',
    keyVerses: ['Ezra 7:10', 'Nehemiah 8:1-8'],
    associatedBooks: ['Ezra', 'Nehemiah'],
    significance: 'Scribe and spiritual reformer',
    timelinePosition: 8
  },
  {
    id: 'stephen',
    name: 'Stephen',
    slug: 'stephen',
    description: 'The first Christian martyr, full of faith and power',
    testament: 'new',
    keyVerses: ['Acts 6:5', 'Acts 7:55-56', 'Acts 7:59-60'],
    associatedBooks: ['Acts'],
    significance: 'First Christian martyr',
    timelinePosition: 11
  },
  {
    id: 'philip-evangelist',
    name: 'Philip the Evangelist',
    slug: 'philip-evangelist',
    description: 'The deacon who evangelized Samaria and the Ethiopian',
    testament: 'new',
    keyVerses: ['Acts 6:5', 'Acts 8:5-8', 'Acts 8:26-40'],
    associatedBooks: ['Acts'],
    significance: 'Early evangelist and deacon',
    timelinePosition: 11
  },
  {
    id: 'apollos',
    name: 'Apollos',
    slug: 'apollos',
    description: 'The eloquent preacher from Alexandria',
    testament: 'new',
    keyVerses: ['Acts 18:24-28', '1 Corinthians 3:5-6'],
    associatedBooks: ['Acts', '1 Corinthians'],
    significance: 'Eloquent preacher and church planter',
    timelinePosition: 11
  }
];

// Helper functions for character data
export const getCharacter = (slug: string): BiblicalCharacter | undefined => {
  return BIBLICAL_CHARACTERS.find(char => char.slug === slug);
};

export const getOldTestamentCharacters = (): BiblicalCharacter[] => {
  return BIBLICAL_CHARACTERS.filter(char => char.testament === 'old' || char.testament === 'both');
};

export const getNewTestamentCharacters = (): BiblicalCharacter[] => {
  return BIBLICAL_CHARACTERS.filter(char => char.testament === 'new' || char.testament === 'both');
};

export const getMajorCharacters = (): BiblicalCharacter[] => {
  const majorIds = ['abraham', 'moses', 'david', 'jesus', 'paul', 'peter', 'john-apostle'];
  return BIBLICAL_CHARACTERS.filter(char => majorIds.includes(char.id));
};

export const getWomenCharacters = (): BiblicalCharacter[] => {
  const womenIds = ['sarah', 'rebekah', 'rachel', 'ruth', 'esther', 'deborah', 'mary-mother', 'mary-magdalene', 'martha'];
  return BIBLICAL_CHARACTERS.filter(char => womenIds.includes(char.id));
};

export const getProphets = (): BiblicalCharacter[] => {
  const prophetIds = ['moses', 'samuel', 'elijah', 'elisha', 'isaiah', 'jeremiah', 'ezekiel', 'daniel', 'jonah', 'john-baptist'];
  return BIBLICAL_CHARACTERS.filter(char => prophetIds.includes(char.id));
};

export const getKings = (): BiblicalCharacter[] => {
  const kingIds = ['saul', 'david', 'solomon', 'hezekiah', 'josiah', 'nebuchadnezzar'];
  return BIBLICAL_CHARACTERS.filter(char => kingIds.includes(char.id));
};

export const getApostles = (): BiblicalCharacter[] => {
  const apostleIds = ['peter', 'john-apostle', 'paul', 'matthew', 'james-apostle', 'thomas'];
  return BIBLICAL_CHARACTERS.filter(char => apostleIds.includes(char.id));
};

// Character quiz generation helpers
export const getCharactersByTimelinePeriod = (period: number): BiblicalCharacter[] => {
  return BIBLICAL_CHARACTERS.filter(char => char.timelinePosition === period);
};

export const getCharactersByBook = (bookSlug: string): BiblicalCharacter[] => {
  return BIBLICAL_CHARACTERS.filter(char => 
    char.associatedBooks.some(book => book.toLowerCase().includes(bookSlug.toLowerCase()))
  );
};

// Constants for character quizzes
export const CHARACTER_CATEGORIES = {
  MAJOR: 'Major Biblical Figures',
  WOMEN: 'Women of the Bible',
  PROPHETS: 'Prophets and Seers',
  KINGS: 'Kings and Rulers',
  APOSTLES: 'Apostles and Disciples',
  OLD_TESTAMENT: 'Old Testament Characters',
  NEW_TESTAMENT: 'New Testament Characters'
} as const;

export const PLATFORM_CHARACTER_STATS = {
  TOTAL_CHARACTERS: BIBLICAL_CHARACTERS.length,
  OLD_TESTAMENT_CHARACTERS: getOldTestamentCharacters().length,
  NEW_TESTAMENT_CHARACTERS: getNewTestamentCharacters().length,
  MAJOR_CHARACTERS: getMajorCharacters().length,
  WOMEN_CHARACTERS: getWomenCharacters().length,
  PROPHETS: getProphets().length,
  KINGS: getKings().length,
  APOSTLES: getApostles().length
} as const;