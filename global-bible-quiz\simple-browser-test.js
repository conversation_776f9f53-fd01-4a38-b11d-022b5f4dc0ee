// Simple browser test using puppeteer or basic curl commands
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

async function simpleBrowserTest() {
  console.log('🔍 Starting Bible Quiz Application Testing...');
  
  const testResults = {
    timestamp: new Date().toISOString(),
    accessiblePort: null,
    pageContent: null,
    httpHeaders: null,
    tests: {}
  };

  // Test port accessibility
  const ports = [3000, 3001, 8080, 8000, 5000];
  
  for (const port of ports) {
    console.log(`\n📡 Testing port ${port}...`);
    
    try {
      const result = await runCommand(`curl -I http://localhost:${port} 2>/dev/null`);
      if (result.includes('200 OK')) {
        console.log(`✅ Successfully accessed port ${port}`);
        testResults.accessiblePort = port;
        testResults.httpHeaders = result;
        break;
      }
    } catch (error) {
      console.log(`❌ Port ${port} not accessible`);
    }
  }

  if (!testResults.accessiblePort) {
    console.log('❌ No accessible ports found. Application may not be running.');
    return testResults;
  }

  const baseUrl = `http://localhost:${testResults.accessiblePort}`;
  console.log(`\n🎯 Using ${baseUrl} for testing`);

  // Get main page content
  try {
    console.log('\n📄 Fetching main page content...');
    const pageContent = await runCommand(`curl -s "${baseUrl}"`);
    testResults.pageContent = pageContent;
    
    // Analyze page content
    testResults.tests.hasTitle = pageContent.includes('<title>');
    testResults.tests.hasNextJs = pageContent.includes('__next') || pageContent.includes('_next');
    testResults.tests.hasReact = pageContent.includes('react') || pageContent.includes('React');
    testResults.tests.hasQuizContent = pageContent.includes('quiz') || pageContent.includes('Quiz');
    testResults.tests.hasBibleContent = pageContent.includes('bible') || pageContent.includes('Bible');
    
    // Extract title
    const titleMatch = pageContent.match(/<title>(.*?)<\/title>/i);
    testResults.tests.pageTitle = titleMatch ? titleMatch[1] : 'No title found';
    
    // Look for quiz-related links
    const linkMatches = pageContent.match(/href="[^"]*(?:quiz|chapter|bible)[^"]*"/gi) || [];
    testResults.tests.quizLinks = linkMatches.map(link => link.replace(/href="|"/g, ''));
    
    console.log(`📋 Page title: ${testResults.tests.pageTitle}`);
    console.log(`🔗 Found ${testResults.tests.quizLinks.length} quiz-related links`);
    
  } catch (error) {
    console.log(`❌ Error fetching main page: ${error.message}`);
    testResults.tests.mainPageError = error.message;
  }

  // Test quiz page if links found
  if (testResults.tests.quizLinks && testResults.tests.quizLinks.length > 0) {
    console.log('\n🎮 Testing quiz pages...');
    
    for (let i = 0; i < Math.min(3, testResults.tests.quizLinks.length); i++) {
      const link = testResults.tests.quizLinks[i];
      const fullUrl = link.startsWith('http') ? link : `${baseUrl}${link}`;
      
      console.log(`\n📍 Testing: ${fullUrl}`);
      
      try {
        const quizPageContent = await runCommand(`curl -s "${fullUrl}"`);
        
        // Analyze quiz page
        const quizTest = {
          hasQuizElements: quizPageContent.includes('question') || quizPageContent.includes('quiz'),
          hasInteractiveElements: quizPageContent.includes('button') || quizPageContent.includes('input'),
          hasTimer: quizPageContent.includes('timer') || quizPageContent.includes('countdown'),
          hasProgress: quizPageContent.includes('progress') || quizPageContent.includes('Progress'),
          contentLength: quizPageContent.length
        };
        
        testResults.tests[`quizPage${i + 1}`] = quizTest;
        
        // Test bypass parameter
        const bypassUrl = fullUrl + (fullUrl.includes('?') ? '&start=true' : '?start=true');
        console.log(`🚀 Testing bypass: ${bypassUrl}`);
        
        const bypassContent = await runCommand(`curl -s "${bypassUrl}"`);
        testResults.tests[`bypassPage${i + 1}`] = {
          contentLength: bypassContent.length,
          different: bypassContent !== quizPageContent
        };
        
      } catch (error) {
        console.log(`❌ Error testing quiz page ${i + 1}: ${error.message}`);
        testResults.tests[`quizPage${i + 1}Error`] = error.message;
      }
    }
  }

  // Test API endpoints
  console.log('\n🔌 Testing API endpoints...');
  const apiEndpoints = ['/api/health', '/api/quiz', '/api/questions'];
  
  for (const endpoint of apiEndpoints) {
    try {
      const apiResponse = await runCommand(`curl -I "${baseUrl}${endpoint}" 2>/dev/null`);
      testResults.tests[`api${endpoint.replace(/[^a-zA-Z0-9]/g, '')}`] = {
        accessible: apiResponse.includes('200') || apiResponse.includes('404'),
        response: apiResponse.split('\n')[0]
      };
    } catch (error) {
      testResults.tests[`api${endpoint.replace(/[^a-zA-Z0-9]/g, '')}Error`] = error.message;
    }
  }

  // Save results
  fs.writeFileSync(
    path.join(__dirname, 'browser-test-results.json'),
    JSON.stringify(testResults, null, 2)
  );

  console.log('\n📊 TEST SUMMARY:');
  console.log(`✅ Accessible Port: ${testResults.accessiblePort}`);
  console.log(`📋 Page Title: ${testResults.tests.pageTitle}`);
  console.log(`🔗 Quiz Links Found: ${testResults.tests.quizLinks?.length || 0}`);
  console.log(`⚛️  Has React/Next.js: ${testResults.tests.hasNextJs}`);
  console.log(`📖 Has Bible Content: ${testResults.tests.hasBibleContent}`);
  console.log(`🎯 Has Quiz Content: ${testResults.tests.hasQuizContent}`);

  return testResults;
}

function runCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error && !stdout) {
        reject(error);
      } else {
        resolve(stdout || stderr);
      }
    });
  });
}

// Run the test
simpleBrowserTest().then(results => {
  console.log('\n✅ Simple browser test completed!');
  console.log('📄 Results saved to browser-test-results.json');
}).catch(error => {
  console.error('❌ Test failed:', error);
});