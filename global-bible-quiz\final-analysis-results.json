{"timestamp": "2025-07-31T04:23:50.658Z", "accessiblePort": 3000, "applicationStatus": "running", "mainPageAnalysis": {"contentLength": 37508, "hasTitle": true, "hasNextJs": true, "hasReactElements": false, "hasQuizLinks": 20, "hasInteractiveElements": true, "pageTitle": "Bible Quiz - Test Your Biblical Knowledge | BibleQuiz", "metaDescription": "Test your Bible knowledge with comprehensive quizzes covering all 66 books. Interactive questions with detailed explanations and scripture references. Perfect for Bible study!", "hasAccessibilityFeatures": true, "hasPWAFeatures": true}, "quizPageAnalysis": {"contentLength": 81254, "questionsFound": 1, "radioInputsFound": 80, "hasProgressBar": false, "hasTimer": false, "hasScriptureReferences": true, "hasInstructions": true, "estimatedQuestions": 20, "hasInteractiveQuiz": true}, "hydrationIssueAnalysis": {"hasNextJsChunks": true, "hasAppChunks": true, "hasHydrationCode": true, "hasInteractiveComponents": true, "hasDynamicImports": false, "hasSSRDisabled": false, "suppressHydrationWarning": false}, "functionalityTests": {"bypassParameter": {"originalLength": 81254, "bypassLength": 81294, "isDifferent": true, "working": true}, "page__matthew_quiz": {"accessible": true, "contentLength": 83979, "hasQuizElements": true, "hasInteractiveElements": true, "title": "<PERSON> Quiz Bible Quiz - Test Your Thematic Knowledge"}, "page__bible_quizzes": {"accessible": true, "contentLength": 46358, "hasQuizElements": true, "hasInteractiveElements": true, "title": "Bible Quiz - Test Your Biblical Knowledge | BibleQuiz"}, "page__genesis_chapters": {"accessible": true, "contentLength": 103459, "hasQuizElements": true, "hasInteractiveElements": true, "title": "Genesis Chapter Quizzes - All 50 Chapters | Bible Quiz"}}, "recommendations": ["Consider adding a visible timer component to quiz pages for better user experience", "Interactive quiz components may benefit from dynamic imports with SSR disabled to prevent hydration issues", "Monitor browser console for hydration warnings during client-side interactions", "Test quiz submission and results display functionality with browser DevTools open"]}