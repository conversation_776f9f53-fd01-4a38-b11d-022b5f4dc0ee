#!/bin/bash

# Production Docker startup script
# This script starts the production environment

echo "🚀 Starting Global Bible Quiz Production Environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Remove any dangling images
echo "🧹 Cleaning up dangling images..."
docker image prune -f

# Build and start production containers
echo "🔨 Building and starting production containers..."
docker-compose -f docker-compose.prod.yml up --build -d

# Show running containers
echo "📋 Running containers:"
docker-compose -f docker-compose.prod.yml ps

# Health check
echo "🏥 Performing health check..."
sleep 10

# Check if the service is healthy
if curl -f http://localhost > /dev/null 2>&1; then
    echo "✅ Production server is healthy and running!"
    echo "🌐 Application available at: http://localhost"
else
    echo "⚠️  Health check failed. Checking logs..."
    docker-compose -f docker-compose.prod.yml logs bible-quiz-prod
fi

echo ""
echo "🎉 Production deployment complete!"
echo "📊 To view logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "🛑 To stop: docker-compose -f docker-compose.prod.yml down"