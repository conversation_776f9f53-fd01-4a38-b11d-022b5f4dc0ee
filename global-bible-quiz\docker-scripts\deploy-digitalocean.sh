#!/bin/bash

# Digital Ocean deployment script for Global Bible Quiz
# This script deploys the application to Digital Ocean App Platform

echo "🌊 Deploying Global Bible Quiz to Digital Ocean..."

# Configuration
DO_APP_NAME=${DO_APP_NAME:-global-bible-quiz}
DO_REGION=${DO_REGION:-nyc1}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-registry.digitalocean.com/bible-quiz}
IMAGE_TAG=${IMAGE_TAG:-latest}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command -v doctl &> /dev/null; then
    echo "❌ doctl (Digital Ocean CLI) not found. Please install doctl first."
    echo "   Installation: https://docs.digitalocean.com/reference/doctl/how-to/install/"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found. Please install Docker first."
    exit 1
fi

# Check DO authentication
if ! doctl auth list > /dev/null 2>&1; then
    echo "❌ Digital Ocean authentication not configured. Please run 'doctl auth init' first."
    exit 1
fi

echo "🏗️  Building production Docker image..."
docker build -f Dockerfile -t ${DO_APP_NAME}:${IMAGE_TAG} .

# Login to Digital Ocean Container Registry
echo "🔐 Logging into Digital Ocean Container Registry..."
doctl registry login

# Tag and push image
echo "🚀 Pushing image to Digital Ocean Container Registry..."
docker tag ${DO_APP_NAME}:${IMAGE_TAG} ${DOCKER_REGISTRY}:${IMAGE_TAG}
docker push ${DOCKER_REGISTRY}:${IMAGE_TAG}

# Create app spec for Digital Ocean App Platform
echo "📋 Creating Digital Ocean App specification..."
cat > app-spec.yaml << EOF
name: ${DO_APP_NAME}
region: ${DO_REGION}
services:
- name: web
  image:
    registry_type: DOCR
    repository: bible-quiz
    tag: ${IMAGE_TAG}
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 3000
  health_check:
    http_path: /
  environment_variables:
  - key: NODE_ENV
    value: production
  - key: NEXT_TELEMETRY_DISABLED
    value: "1"
  routes:
  - path: /
domains:
- name: globalbiblequiz.com
  type: PRIMARY
  wildcard: false
EOF

# Deploy or update the app
echo "🚀 Deploying to Digital Ocean App Platform..."

# Check if app already exists
if doctl apps list | grep -q ${DO_APP_NAME}; then
    echo "📝 Updating existing app..."
    APP_ID=$(doctl apps list --format ID,Spec.Name --no-header | grep ${DO_APP_NAME} | awk '{print $1}')
    doctl apps update ${APP_ID} --spec app-spec.yaml
else
    echo "🆕 Creating new app..."
    doctl apps create --spec app-spec.yaml
fi

# Get app URL
echo "⏳ Waiting for deployment to complete..."
sleep 30

APP_ID=$(doctl apps list --format ID,Spec.Name --no-header | grep ${DO_APP_NAME} | awk '{print $1}')
APP_URL=$(doctl apps get ${APP_ID} --format LiveURL --no-header)

echo ""
echo "✅ Deployment to Digital Ocean completed successfully!"
echo "🌐 Your Global Bible Quiz is available at: ${APP_URL}"
echo "📊 Monitor your app: https://cloud.digitalocean.com/apps/${APP_ID}"
echo ""
echo "🧹 Cleaning up temporary files..."
rm -f app-spec.yaml

echo "🎉 Digital Ocean deployment finished!"